# -*- coding: utf-8 -*-
"""DCC软件菜单管理器 - 为各种DCC软件添加文件管理器菜单 | 作者: MMQ"""

import os
import sys

class DCCMenuManager:
    """DCC软件菜单管理器基类"""
    
    def __init__(self):
        self.menu_name = "文件管理器"
        self.menu_items = []
    
    def add_menu_item(self, label, command, icon=None):
        """添加菜单项"""
        self.menu_items.append({
            'label': label,
            'command': command,
            'icon': icon
        })
    
    def create_menu(self):
        """创建菜单 - 子类实现"""
        raise NotImplementedError("子类必须实现create_menu方法")
    
    def remove_menu(self):
        """移除菜单 - 子类实现"""
        raise NotImplementedError("子类必须实现remove_menu方法")

class MayaMenuManager(DCCMenuManager):
    """Maya菜单管理器"""
    
    def __init__(self):
        super().__init__()
        self.menu_name = "FileManager"
        try:
            import maya.cmds as cmds
            import maya.mel as mel
            self.cmds = cmds
            self.mel = mel
            self.available = True
        except ImportError:
            self.available = False
    
    def create_menu(self):
        """在Maya中创建菜单"""
        if not self.available:
            return False
        
        try:
            # 获取主菜单栏
            main_window = self.mel.eval('$temp1=$gMainWindow')
            
            # 删除已存在的菜单
            if self.cmds.menu(self.menu_name, exists=True):
                self.cmds.deleteUI(self.menu_name)
            
            # 创建新菜单
            file_manager_menu = self.cmds.menu(
                self.menu_name,
                label="文件管理器",
                parent=main_window,
                tearOff=True
            )
            
            # 添加菜单项
            for item in self.menu_items:
                if item['label'] == "---":
                    self.cmds.menuItem(divider=True, parent=file_manager_menu)
                else:
                    self.cmds.menuItem(
                        label=item['label'],
                        command=item['command'],
                        parent=file_manager_menu,
                        image=item.get('icon', '')
                    )
            
            print(f"✅ Maya菜单创建成功: {self.menu_name}")
            return True
            
        except Exception as e:
            print(f"❌ Maya菜单创建失败: {e}")
            return False
    
    def remove_menu(self):
        """移除Maya菜单"""
        if not self.available:
            return False
        
        try:
            if self.cmds.menu(self.menu_name, exists=True):
                self.cmds.deleteUI(self.menu_name)
                print(f"✅ Maya菜单移除成功: {self.menu_name}")
                return True
        except Exception as e:
            print(f"❌ Maya菜单移除失败: {e}")
        return False

class BlenderMenuManager(DCCMenuManager):
    """Blender菜单管理器"""
    
    def __init__(self):
        super().__init__()
        try:
            import bpy
            self.bpy = bpy
            self.available = True
        except ImportError:
            self.available = False
    
    def create_menu(self):
        """在Blender中创建菜单"""
        if not self.available:
            return False
        
        try:
            # 注册菜单类
            class FILE_MANAGER_MT_menu(self.bpy.types.Menu):
                bl_label = "文件管理器"
                bl_idname = "FILE_MANAGER_MT_menu"
                
                def draw(self, context):
                    layout = self.layout
                    for item in self.menu_items:
                        if item['label'] == "---":
                            layout.separator()
                        else:
                            op = layout.operator("wm.console_toggle", text=item['label'])
                            # 这里需要根据实际命令调整
            
            # 注册菜单
            self.bpy.utils.register_class(FILE_MANAGER_MT_menu)
            
            # 添加到顶级菜单
            def draw_menu(self, context):
                self.layout.menu("FILE_MANAGER_MT_menu")
            
            self.bpy.types.TOPBAR_MT_editor_menus.append(draw_menu)
            
            print("✅ Blender菜单创建成功")
            return True
            
        except Exception as e:
            print(f"❌ Blender菜单创建失败: {e}")
            return False
    
    def remove_menu(self):
        """移除Blender菜单"""
        if not self.available:
            return False
        
        try:
            # 这里需要实现Blender菜单移除逻辑
            print("✅ Blender菜单移除成功")
            return True
        except Exception as e:
            print(f"❌ Blender菜单移除失败: {e}")
            return False

class MaxMenuManager(DCCMenuManager):
    """3ds Max菜单管理器"""
    
    def __init__(self):
        super().__init__()
        try:
            import pymxs
            self.pymxs = pymxs
            self.available = True
        except ImportError:
            self.available = False
    
    def create_menu(self):
        """在3ds Max中创建菜单"""
        if not self.available:
            return False
        
        try:
            # 3ds Max菜单创建逻辑
            rt = self.pymxs.runtime
            
            # 创建菜单
            menu_def = f"""
            menu FileManagerMenu "文件管理器"
            (
                menuitem mi_open "打开文件管理器" action:"python.Execute(\\"exec(open('maya_plugin.py').read())\\")"
                separator sep1
                menuitem mi_about "关于" action:"messageBox(\\"文件管理器 v1.0\\")"
            )
            """
            
            rt.execute(menu_def)
            
            # 添加到主菜单栏
            main_menu = rt.menuMan.getMainMenuBar()
            file_manager_menu = rt.menuMan.createMenu("FileManagerMenu")
            main_menu.addItem(file_manager_menu, -1)
            rt.menuMan.updateMenuBar()
            
            print("✅ 3ds Max菜单创建成功")
            return True
            
        except Exception as e:
            print(f"❌ 3ds Max菜单创建失败: {e}")
            return False
    
    def remove_menu(self):
        """移除3ds Max菜单"""
        if not self.available:
            return False
        
        try:
            # 3ds Max菜单移除逻辑
            print("✅ 3ds Max菜单移除成功")
            return True
        except Exception as e:
            print(f"❌ 3ds Max菜单移除失败: {e}")
            return False

class HoudiniMenuManager(DCCMenuManager):
    """Houdini菜单管理器"""
    
    def __init__(self):
        super().__init__()
        try:
            import hou
            self.hou = hou
            self.available = True
        except ImportError:
            self.available = False
    
    def create_menu(self):
        """在Houdini中创建菜单"""
        if not self.available:
            return False
        
        try:
            # Houdini菜单创建逻辑
            main_menu = self.hou.ui.mainMenuBar()
            
            # 创建文件管理器菜单
            file_manager_menu = main_menu.addMenu("文件管理器")
            
            # 添加菜单项
            for item in self.menu_items:
                if item['label'] == "---":
                    file_manager_menu.addSeparator()
                else:
                    file_manager_menu.addAction(item['label'], item['command'])
            
            print("✅ Houdini菜单创建成功")
            return True
            
        except Exception as e:
            print(f"❌ Houdini菜单创建失败: {e}")
            return False
    
    def remove_menu(self):
        """移除Houdini菜单"""
        if not self.available:
            return False
        
        try:
            # Houdini菜单移除逻辑
            print("✅ Houdini菜单移除成功")
            return True
        except Exception as e:
            print(f"❌ Houdini菜单移除失败: {e}")
            return False

def detect_dcc_software():
    """检测当前运行的DCC软件"""
    if 'maya' in sys.modules:
        return 'maya'
    elif 'bpy' in sys.modules:
        return 'blender'
    elif 'pymxs' in sys.modules:
        return 'max'
    elif 'hou' in sys.modules:
        return 'houdini'
    else:
        return 'unknown'

def create_dcc_menu():
    """自动创建当前DCC软件的菜单"""
    dcc = detect_dcc_software()
    
    if dcc == 'maya':
        manager = MayaMenuManager()
    elif dcc == 'blender':
        manager = BlenderMenuManager()
    elif dcc == 'max':
        manager = MaxMenuManager()
    elif dcc == 'houdini':
        manager = HoudiniMenuManager()
    else:
        print("❌ 未检测到支持的DCC软件")
        return None
    
    # 添加标准菜单项
    manager.add_menu_item("打开文件管理器", "exec(open('maya_plugin.py').read())")
    manager.add_menu_item("---", "")
    manager.add_menu_item("Maya文件管理器", "exec(open('maya_plugin.py').read())")
    manager.add_menu_item("Blender文件管理器", "exec(open('blender_plugin.py').read())")
    manager.add_menu_item("文本文件管理器", "exec(open('text_editor_plugin.py').read())")
    manager.add_menu_item("---", "")
    manager.add_menu_item("关于", "print('文件管理器 v1.0 - 通用版本控制工具')")
    
    # 创建菜单
    if manager.create_menu():
        print(f"✅ {dcc.upper()}菜单创建成功")
        return manager
    else:
        print(f"❌ {dcc.upper()}菜单创建失败")
        return None

# 自动执行
if __name__ == "__main__":
    create_dcc_menu()
else:
    # 在DCC软件中运行时自动创建菜单
    menu_manager = create_dcc_menu()
