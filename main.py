# -*- coding: utf-8 -*-
"""文件管理器主入口 | 作者: MMQ"""

from core import get_current_software, get_project_path

def load_plugin(software):
    """加载插件"""
    try:
        if software == 'maya':
            from plugins.maya_plugin import MayaPlugin
            return MayaPlugin()
        elif software == 'blender':
            from plugins.blender_plugin import BlenderPlugin
            return BlenderPlugin()
        return None
    except:
        return None

def load_menu(software):
    """加载菜单"""
    try:
        if software == 'maya':
            from menus.maya_menu import MayaMenu
            return MayaMenu()
        elif software == 'blender':
            from menus.blender_menu import BlenderMenu
            return BlenderMenu()
        return None
    except:
        return None

def create_file_manager(software, plugin, menu_manager=None):
    """创建文件管理器"""
    try:
        from mvc.controller import FileManagerController

        project_path = get_project_path()
        controller = FileManagerController(
            plugin=plugin,
            save_dir=project_path,
            base_name=f"{software}_project"
        )

        if menu_manager:
            menu_manager.create_menu(controller)

        return controller
    except Exception as e:
        print(f"创建失败: {e}")
        return None

def main():
    """主函数"""
    software = get_current_software()
    plugin = load_plugin(software)
    if not plugin:
        print("插件加载失败")
        return None

    menu_manager = load_menu(software)
    controller = create_file_manager(software, plugin, menu_manager)
    if controller:
        controller.show()
    return controller

file_manager_instance = None

def start():
    """启动"""
    global file_manager_instance
    file_manager_instance = main()
    return file_manager_instance

def restart():
    """重启"""
    global file_manager_instance
    if file_manager_instance:
        try:
            file_manager_instance.close()
        except:
            pass
    file_manager_instance = main()
    return file_manager_instance

if __name__ == "__main__":
    start()
