# -*- coding: utf-8 -*-
"""文件管理器主入口 | 作者: MMQ"""

import sys
import os
from core import get_current_software, get_project_path
from utils import VersionCalculator

def detect_software():
    """检测当前运行的软件环境"""
    software = get_current_software()
    print(f"检测到软件: {software}")
    return software

def load_plugin(software):
    """根据软件类型加载对应插件"""
    try:
        if software == 'maya':
            from plugins.maya_plugin import MayaPlugin
            return MayaPlugin()
        elif software == 'blender':
            from plugins.blender_plugin import BlenderPlugin
            return BlenderPlugin()
        else:
            print(f"不支持的软件: {software}")
            return None
    except ImportError as e:
        print(f"插件加载失败: {e}")
        return None

def load_menu(software):
    """根据软件类型加载对应菜单"""
    try:
        if software == 'maya':
            from menus.maya_menu import MayaMenu
            return MayaMenu()
        elif software == 'blender':
            from menus.blender_menu import BlenderMenu
            return BlenderMenu()
        else:
            print(f"不支持的软件菜单: {software}")
            return None
    except ImportError as e:
        print(f"菜单加载失败: {e}")
        return None

def create_file_manager(software, plugin, menu_manager=None):
    """创建文件管理器实例"""
    try:
        from mvc.controller import FileManagerController
        
        # 获取项目路径
        project_path = get_project_path()
        
        # 创建控制器
        controller = FileManagerController(
            plugin=plugin,
            save_dir=project_path,
            base_name=f"{software}_project"
        )
        
        # 如果有菜单管理器，创建菜单
        if menu_manager:
            menu_manager.create_menu(controller)
        
        return controller
        
    except Exception as e:
        print(f"文件管理器创建失败: {e}")
        return None

def main():
    """主函数"""
    print("🚀 启动文件管理器...")
    
    # 1. 检测软件环境
    software = detect_software()
    if not software:
        print("❌ 无法检测软件环境")
        return
    
    # 2. 加载插件
    plugin = load_plugin(software)
    if not plugin:
        print("❌ 插件加载失败")
        return
    
    # 3. 加载菜单
    menu_manager = load_menu(software)
    
    # 4. 创建文件管理器
    controller = create_file_manager(software, plugin, menu_manager)
    if not controller:
        print("❌ 文件管理器创建失败")
        return
    
    # 5. 显示界面
    controller.show()
    print("✅ 文件管理器启动成功")
    
    return controller

# 全局变量
file_manager_instance = None

def start():
    """启动入口函数"""
    global file_manager_instance
    file_manager_instance = main()
    return file_manager_instance

def restart():
    """重启文件管理器"""
    global file_manager_instance
    if file_manager_instance:
        try:
            file_manager_instance.close()
        except:
            pass
    file_manager_instance = main()
    return file_manager_instance

def stop():
    """停止文件管理器"""
    global file_manager_instance
    if file_manager_instance:
        try:
            file_manager_instance.close()
            file_manager_instance = None
            print("✅ 文件管理器已停止")
        except Exception as e:
            print(f"❌ 停止失败: {e}")

# 中文函数别名
启动 = start
重启 = restart
停止 = stop

if __name__ == "__main__":
    start()
