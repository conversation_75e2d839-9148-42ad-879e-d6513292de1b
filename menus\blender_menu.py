# -*- coding: utf-8 -*-
"""Blender菜单管理器 | 作者: MMQ"""

class BlenderMenu:
    """Blender菜单管理器"""
    
    def __init__(self):
        self.menu_name = "FILE_MANAGER_MT_menu"
        self.menu_label = "文件管理器"
        self.available = False
        self.menu_class = None
        
        try:
            import bpy
            self.bpy = bpy
            self.available = True
        except ImportError:
            pass
    
    def create_menu(self, controller=None):
        """创建Blender菜单"""
        if not self.available:
            return False
        
        try:
            # 定义菜单类
            class FILE_MANAGER_MT_menu(self.bpy.types.Menu):
                bl_label = "文件管理器"
                bl_idname = "FILE_MANAGER_MT_menu"
                
                def draw(self, context):
                    layout = self.layout
                    
                    # 打开文件管理器
                    layout.operator("wm.console_toggle", text="打开文件管理器")
                    
                    layout.separator()
                    
                    # 版本操作
                    layout.operator("wm.console_toggle", text="新建版本")
                    layout.operator("wm.console_toggle", text="保存版本")
                    layout.operator("wm.console_toggle", text="打开最新版本")
                    
                    layout.separator()
                    
                    # 文件夹操作
                    layout.operator("wm.console_toggle", text="打开项目文件夹")
                    
                    layout.separator()
                    
                    # 设置和关于
                    layout.operator("wm.console_toggle", text="设置")
                    layout.operator("wm.console_toggle", text="关于")
            
            # 保存菜单类引用
            self.menu_class = FILE_MANAGER_MT_menu
            
            # 注册菜单类
            if hasattr(self.bpy.utils, 'register_class'):
                self.bpy.utils.register_class(FILE_MANAGER_MT_menu)
            
            # 添加到顶级菜单
            def draw_menu(self, context):
                self.layout.menu("FILE_MANAGER_MT_menu")
            
            # 添加到文件菜单
            if hasattr(self.bpy.types, 'TOPBAR_MT_file'):
                self.bpy.types.TOPBAR_MT_file.append(draw_menu)
            elif hasattr(self.bpy.types, 'INFO_MT_file'):
                self.bpy.types.INFO_MT_file.append(draw_menu)
            
            print(f"✅ Blender菜单创建成功: {self.menu_label}")
            return True
            
        except Exception as e:
            print(f"❌ Blender菜单创建失败: {e}")
            return False
    
    def remove_menu(self):
        """移除Blender菜单"""
        if not self.available:
            return False
        
        try:
            # 从菜单中移除
            def draw_menu(self, context):
                self.layout.menu("FILE_MANAGER_MT_menu")
            
            if hasattr(self.bpy.types, 'TOPBAR_MT_file'):
                self.bpy.types.TOPBAR_MT_file.remove(draw_menu)
            elif hasattr(self.bpy.types, 'INFO_MT_file'):
                self.bpy.types.INFO_MT_file.remove(draw_menu)
            
            # 注销菜单类
            if self.menu_class and hasattr(self.bpy.utils, 'unregister_class'):
                self.bpy.utils.unregister_class(self.menu_class)
            
            print(f"✅ Blender菜单移除成功: {self.menu_label}")
            return True
            
        except Exception as e:
            print(f"❌ Blender菜单移除失败: {e}")
            return False
    
    def create_operators(self, controller):
        """创建Blender操作符"""
        if not self.available:
            return False
        
        try:
            # 定义文件管理器操作符
            class FILE_MANAGER_OT_open(self.bpy.types.Operator):
                bl_idname = "file_manager.open"
                bl_label = "打开文件管理器"
                bl_description = "打开文件管理器界面"
                
                def execute(self, context):
                    if controller:
                        controller.show()
                    return {'FINISHED'}
            
            class FILE_MANAGER_OT_save(self.bpy.types.Operator):
                bl_idname = "file_manager.save"
                bl_label = "保存版本"
                bl_description = "保存当前文件为新版本"
                
                def execute(self, context):
                    if controller:
                        controller.save_current_version()
                    return {'FINISHED'}
            
            class FILE_MANAGER_OT_open_latest(self.bpy.types.Operator):
                bl_idname = "file_manager.open_latest"
                bl_label = "打开最新版本"
                bl_description = "打开最新版本文件"
                
                def execute(self, context):
                    if controller:
                        controller.open_latest_version()
                    return {'FINISHED'}
            
            # 注册操作符
            operators = [
                FILE_MANAGER_OT_open,
                FILE_MANAGER_OT_save,
                FILE_MANAGER_OT_open_latest
            ]
            
            for op in operators:
                if hasattr(self.bpy.utils, 'register_class'):
                    self.bpy.utils.register_class(op)
            
            return True
            
        except Exception as e:
            print(f"❌ Blender操作符创建失败: {e}")
            return False
    
    def update_menu_state(self, controller):
        """更新菜单状态"""
        # Blender菜单状态更新逻辑
        pass
    
    def add_custom_menu_item(self, label, operator_id, icon=None):
        """添加自定义菜单项"""
        if not self.available:
            return False
        
        try:
            # 这里可以动态添加菜单项的逻辑
            return True
        except Exception as e:
            print(f"添加菜单项失败: {e}")
            return False
