@echo off
chcp 65001 >nul
echo ================================================
echo Maya文件管理器插件安装程序
echo ================================================
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python。请确保Python已安装并添加到PATH环境变量中。
    echo.
    pause
    exit /b 1
)

echo 正在安装Maya文件管理器插件...
echo.

REM 运行安装脚本
python install_plugin.py

echo.
echo 安装完成！请重启Maya以加载插件。
echo.
pause
