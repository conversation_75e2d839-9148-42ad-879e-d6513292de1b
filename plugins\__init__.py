# -*- coding: utf-8 -*-
"""插件模块 | 作者: MMQ"""

from plugins.base_plugin import PluginBase

def create_plugin(software):
    """插件工厂函数"""
    try:
        if software == 'maya':
            from plugins.maya_plugin import MayaPlugin
            return MayaPlugin()
        elif software == 'blender':
            from plugins.blender_plugin import BlenderPlugin
            return BlenderPlugin()
        elif software == 'text':
            from plugins.text_plugin import TextPlugin
            return TextPlugin()
        elif software == 'python':
            from plugins.text_plugin import PythonPlugin
            return PythonPlugin()
        else:
            # 默认返回文本插件
            from plugins.text_plugin import TextPlugin
            return TextPlugin()
    except ImportError:
        # 如果特定插件导入失败，返回文本插件作为备选
        from plugins.text_plugin import TextPlugin
        return TextPlugin()

def get_available_plugins():
    """获取可用插件列表"""
    plugins = []
    
    # 检查Maya插件
    try:
        from plugins.maya_plugin import MayaPlugin
        plugin = MayaPlugin()
        if plugin.is_available():
            plugins.append(('maya', plugin.get_name()))
    except:
        pass
    
    # 检查Blender插件
    try:
        from plugins.blender_plugin import BlenderPlugin
        plugin = BlenderPlugin()
        if plugin.is_available():
            plugins.append(('blender', plugin.get_name()))
    except:
        pass
    
    # 文本插件总是可用
    plugins.append(('text', '文本编辑器'))
    plugins.append(('python', 'Python脚本编辑器'))
    
    return plugins
