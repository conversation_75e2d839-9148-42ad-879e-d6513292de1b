# -*- coding: utf-8 -*-
"""Maya文件管理器插件 | 作者: MMQ"""

import os
from universal_file_manager import FileManagerPlugin

try:
    import maya.cmds as cmds
    from maya.app.general.mayaMixin import MayaQWidgetDockableMixin
    MAYA_AVAILABLE = True
except ImportError:
    MAYA_AVAILABLE = False

class MayaPlugin(FileManagerPlugin):
    """Maya文件管理器插件"""
    
    def get_name(self):
        """返回插件名称"""
        return "Maya 2022.5.1"
    
    def get_file_extension(self):
        """返回文件扩展名"""
        return ".ma"
    
    def open_file(self, file_path):
        """打开Maya文件"""
        if not MAYA_AVAILABLE:
            return False
        try:
            cmds.file(file_path, open=True, force=True)
            return True
        except:
            return False
    
    def save_file(self, file_path):
        """保存Maya文件"""
        if not MAYA_AVAILABLE:
            return False
        try:
            cmds.file(rename=file_path)
            cmds.file(save=True, type="mayaAscii")
            return True
        except:
            return False
    
    def check_unsaved_changes(self):
        """检查是否有未保存的更改"""
        if not MAYA_AVAILABLE:
            return False
        
        try:
            return cmds.file(query=True, modified=True)
        except:
            return False
    
    def get_current_file_info(self):
        """获取当前文件信息"""
        if not MAYA_AVAILABLE:
            return "Maya不可用"
        
        try:
            current_file = cmds.file(query=True, sceneName=True)
            if current_file:
                return f"当前文件: {os.path.basename(current_file)}"
            else:
                return "当前文件: 未保存的场景"
        except:
            return "无法获取文件信息"

class MayaFileManagerWindow(MayaQWidgetDockableMixin):
    """Maya可停靠的文件管理器窗口"""
    
    def __init__(self, save_dir=None, base_name=None):
        super().__init__()
        
        # 导入通用文件管理器
        from universal_file_manager import UniversalFileManager
        
        # 创建Maya插件
        self.maya_plugin = MayaPlugin()
        
        # 设置默认值
        if not save_dir:
            save_dir = "D:/dev/Scgtest/0010/3d/mm/"
        if not base_name:
            base_name = "dev_cgtest_0010_mm"
        
        # 创建文件管理器
        self.file_manager = UniversalFileManager(
            plugin=self.maya_plugin,
            save_dir=save_dir,
            base_name=base_name,
            parent=self
        )
        
        # 设置窗口属性
        self.setWindowTitle("Maya文件管理器")
        self.setObjectName("MayaFileManagerWindow")
        
        # 设置布局
        from PySide2.QtWidgets import QVBoxLayout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.file_manager)

# 全局变量
maya_file_manager = None

def close_existing():
    """关闭已存在的窗口"""
    if not MAYA_AVAILABLE:
        return
    
    try:
        for name in ["MayaFileManagerWindowWorkspaceControl", "MayaFileManagerWindow"]:
            if (cmds.workspaceControl(name, exists=True) if "Workspace" in name else cmds.window(name, exists=True)):
                cmds.deleteUI(name)
                print(f"已删除: {name}")
    except: 
        pass

def show_maya_file_manager(save_dir=None, base_name=None):
    """显示Maya文件管理器"""
    global maya_file_manager
    
    if not MAYA_AVAILABLE:
        print("❌ Maya不可用，无法启动Maya文件管理器")
        return None
    
    try:
        close_existing()
        if maya_file_manager:
            try:
                maya_file_manager.close()
                maya_file_manager.deleteLater()
            except: 
                pass
            maya_file_manager = None
        
        maya_file_manager = MayaFileManagerWindow(save_dir, base_name)
        maya_file_manager.show(dockable=True)
        print("✅ Maya文件管理器启动成功！")
        return maya_file_manager
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return None

def show_standalone_manager(save_dir=None, base_name=None):
    """显示独立的文件管理器（不依赖Maya）"""
    try:
        from PySide2.QtWidgets import QApplication
        import sys
        
        # 创建应用程序（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建Maya插件（即使Maya不可用也能创建）
        maya_plugin = MayaPlugin()
        
        # 导入通用文件管理器
        from universal_file_manager import UniversalFileManager
        
        # 创建文件管理器
        manager = UniversalFileManager(
            plugin=maya_plugin,
            save_dir=save_dir or os.path.expanduser("~/Documents/Maya_Projects"),
            base_name=base_name or "maya_project"
        )
        
        manager.show()
        print("✅ 独立文件管理器启动成功！")
        
        # 如果是独立运行，启动事件循环
        if __name__ == "__main__":
            sys.exit(app.exec_())
        
        return manager
    except Exception as e:
        print(f"❌ 启动独立管理器失败: {e}")
        return None

# 中文函数别名
启动Maya文件管理器 = lambda: show_maya_file_manager()
启动独立管理器 = lambda: show_standalone_manager()
关闭现有窗口 = close_existing

# 自动启动逻辑
if __name__ == "__main__":
    # 独立运行时启动独立管理器
    show_standalone_manager()
else:
    # 在Maya中运行时自动启动
    if MAYA_AVAILABLE:
        print("正在启动Maya文件管理器...")
        try:
            window = show_maya_file_manager()
            if window:
                print("🎉 Maya文件管理器界面已显示")
                print("📋 功能说明:")
                print("   • iPhone式版本选择器")
                print("   • 颜色区分版本状态")
                print("   • 可调整界面大小")
                print("   • 跨平台文件夹打开")
            else:
                print("❌ 启动失败")
        except Exception as e:
            print(f"❌ 启动异常: {e}")
        
        print("=" * 50)
        print("重启命令: show_maya_file_manager() 或 启动Maya文件管理器()")
        print("独立运行: show_standalone_manager() 或 启动独立管理器()")
        print("=" * 50)
    else:
        print("Maya不可用，可以使用 show_standalone_manager() 启动独立版本")
