# -*- coding: utf-8 -*-
"""
Maya文件管理器 - 优化版本
作者: MMQ
适用于: Maya 2022.5.1

功能完整的文件管理器，代码经过优化
"""

print("🚀 正在启动Maya文件管理器...")

# 导入模块
try:
    import maya.cmds as cmds
    import maya.mel as mel
    from maya.app.general.mayaMixin import MayaQWidgetDockableMixin
    from PySide2.QtWidgets import *
    from PySide2.QtCore import *
    from PySide2.QtGui import *
    import os, datetime
    print("✅ 模块导入成功")
except ImportError as e:
    print("❌ 模块导入失败: " + str(e))

class MayaFileManager(MayaQWidgetDockableMixin, QWidget):
    """Maya文件管理器主界面"""
    
    def __init__(self):
        super(MayaFileManager, self).__init__()
        self.setWindowTitle("Maya文件管理器 v1.0")
        self.setMinimumSize(450, 400)
        self.setObjectName("MayaFileManagerOptimized")
        
        # 项目配置
        self.save_dir = "D:/dev/Scgtest/0010/3d/mm/"
        self.base_name = "dev_cgtest_0010_mm"
        self.log_expanded = True
        
        self.create_ui()
        self.update_versions()
        self.update_file_info()
        self.log("文件管理器启动成功！Maya版本: " + str(cmds.about(version=True)))
        
    def create_ui(self):
        """创建用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(8)
        layout.setContentsMargins(8, 8, 8, 8)
        
        # Maya原生样式
        self.setStyleSheet("""
            QWidget { background-color: #393939; color: #CCCCCC; font-family: "Segoe UI"; font-size: 11px; }
            QPushButton { background-color: #4A4A4A; border: 1px solid #5A5A5A; color: #CCCCCC; 
                         padding: 6px 12px; border-radius: 3px; }
            QPushButton:hover { background-color: #5A5A5A; border-color: #6A6A6A; }
            QPushButton:pressed { background-color: #3A3A3A; }
            QComboBox { background-color: #4A4A4A; border: 1px solid #5A5A5A; color: #CCCCCC; 
                       padding: 4px 8px; border-radius: 3px; }
            QTextEdit { background-color: #2A2A2A; border: 1px solid #5A5A5A; color: #CCCCCC; 
                       padding: 4px; border-radius: 3px; }
            QLabel { color: #CCCCCC; background-color: transparent; }
        """)
        
        # 标题
        title = QLabel("文件管理器")
        title.setStyleSheet("font-size: 12px; font-weight: bold; color: #FFFFFF; padding: 4px 0px; border-bottom: 1px solid #5A5A5A; margin-bottom: 8px;")
        layout.addWidget(title)
        
        # 打开文件区域
        open_layout = QHBoxLayout()
        self.open_btn = QPushButton("打开Maya文件")
        self.open_btn.setMinimumHeight(28)
        self.open_btn.clicked.connect(self.open_file)
        open_layout.addWidget(self.open_btn, 2)
        
        open_ver_layout = QVBoxLayout()
        open_ver_layout.addWidget(QLabel("打开版本:"))
        self.open_version_combo = QComboBox()
        self.open_version_combo.setMinimumHeight(24)
        open_ver_layout.addWidget(self.open_version_combo)
        open_layout.addLayout(open_ver_layout, 1)
        layout.addLayout(open_layout)
        
        # 保存文件区域
        save_layout = QHBoxLayout()
        self.save_btn = QPushButton("保存文件")
        self.save_btn.setMinimumHeight(28)
        self.save_btn.clicked.connect(self.save_file)
        save_layout.addWidget(self.save_btn, 2)
        
        save_ver_layout = QVBoxLayout()
        save_ver_layout.addWidget(QLabel("版本号:"))
        self.version_combo = QComboBox()
        self.version_combo.setMinimumHeight(24)
        self.version_combo.currentTextChanged.connect(lambda v: self.log("版本号已切换到: " + v) if v else None)
        save_ver_layout.addWidget(self.version_combo)
        save_layout.addLayout(save_ver_layout, 1)
        layout.addLayout(save_layout)
        
        # 文件信息
        self.file_info = QLabel("当前文件: 新建场景")
        self.file_info.setStyleSheet("background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 6px; padding: 10px; font-size: 12px;")
        self.file_info.setWordWrap(True)
        layout.addWidget(self.file_info)
        
        # 可折叠日志区域
        log_header = QHBoxLayout()
        self.log_toggle_btn = QPushButton("▼ 操作日志")
        self.log_toggle_btn.setStyleSheet("text-align: left; padding: 4px 8px; font-weight: bold; border: none; background-color: transparent;")
        self.log_toggle_btn.clicked.connect(self.toggle_log)
        log_header.addWidget(self.log_toggle_btn)
        log_header.addStretch()
        layout.addLayout(log_header)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(120)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
    def toggle_log(self):
        """切换日志显示"""
        if self.log_expanded:
            self.log_text.hide()
            self.log_toggle_btn.setText("▶ 操作日志")
        else:
            self.log_text.show()
            self.log_toggle_btn.setText("▼ 操作日志")
        self.log_expanded = not self.log_expanded
        
    def log(self, message):
        """记录日志"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        print(f"[文件管理器] {message}")
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())
        
    def update_file_info(self):
        """更新文件信息"""
        try:
            current_scene = cmds.file(query=True, sceneName=True)
            if current_scene:
                file_name = os.path.basename(current_scene)
                self.file_info.setText(f"当前文件: {file_name}\n路径: {current_scene}")
            else:
                self.file_info.setText("当前文件: 新建场景 (未保存)")
        except Exception as e:
            self.file_info.setText(f"获取文件信息失败: {str(e)}")
            
    def get_existing_versions(self):
        """获取已存在的版本号"""
        versions = []
        if os.path.exists(self.save_dir):
            try:
                for file in os.listdir(self.save_dir):
                    if file.startswith(self.base_name) and file.endswith(('.ma', '.mb')):
                        parts = file.split('_')
                        if len(parts) >= 5 and parts[4].startswith('v') and parts[4][1:].isdigit():
                            versions.append(parts[4])
            except Exception as e:
                self.log(f"读取目录错误: {str(e)}")
        return sorted(list(set(versions)))
    
    def get_next_version(self, existing_versions):
        """获取下一个版本号"""
        if not existing_versions:
            return "v001"
        max_version = max([int(v[1:]) for v in existing_versions if v[1:].isdigit()] or [0])
        return f"v{max_version + 1:03d}"
    
    def update_versions(self):
        """更新版本号下拉菜单"""
        try:
            existing_versions = self.get_existing_versions()
            next_version = self.get_next_version(existing_versions)
            
            # 更新保存版本下拉菜单
            self.version_combo.clear()
            for version in existing_versions + [next_version]:
                self.version_combo.addItem(version)
            if self.version_combo.count() > 0:
                self.version_combo.setCurrentIndex(self.version_combo.count() - 1)
            
            # 更新打开版本下拉菜单
            self.open_version_combo.clear()
            if existing_versions:
                for version in existing_versions:
                    self.open_version_combo.addItem(version)
                self.open_version_combo.setCurrentIndex(self.open_version_combo.count() - 1)
            else:
                self.open_version_combo.addItem("v001")
                
            self.log(f"版本列表已更新，保存版本: {self.version_combo.currentText()}, 打开版本: {self.open_version_combo.currentText()}")
            
        except Exception as e:
            self.log(f"更新版本列表错误: {str(e)}")
            for combo in [self.version_combo, self.open_version_combo]:
                combo.clear()
                combo.addItem("v001")
    
    def generate_filename(self, version=None):
        """生成文件名"""
        version = version or self.version_combo.currentText()
        return f"{self.base_name}_{version}_mmq.ma"
    
    def extract_version_from_filename(self, filename):
        """从文件名提取版本号"""
        try:
            parts = filename.split('_')
            for part in parts:
                if part.startswith('v') and part[1:].isdigit():
                    return part
        except:
            pass
        return None
    
    def check_file_exists_and_confirm(self, file_path, version):
        """检查文件是否存在并确认覆盖"""
        if os.path.exists(file_path):
            reply = QMessageBox.question(
                self, "文件已存在",
                f"版本 {version} 已存在，是否覆盖此文件？\n\n文件: {os.path.basename(file_path)}",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.No:
                self.log("用户取消了文件覆盖")
                return False
        return True

    def open_file(self):
        """打开指定版本的文件"""
        try:
            selected_version = self.open_version_combo.currentText()
            filename = self.generate_filename(selected_version)
            file_path = os.path.join(self.save_dir, filename)

            if not os.path.exists(file_path):
                QMessageBox.warning(self, "文件不存在", f"版本 {selected_version} 的文件不存在:\n{filename}")
                return

            if cmds.file(query=True, modified=True):
                reply = QMessageBox.question(self, "确认", "当前场景有未保存的更改。是否继续？", QMessageBox.Yes | QMessageBox.No)
                if reply == QMessageBox.No:
                    self.log("用户取消了文件打开")
                    return

            cmds.file(file_path, open=True, force=True)
            self.log(f"成功打开版本 {selected_version}: {filename}")
            self.update_file_info()
            QMessageBox.information(self, "成功", f"已打开版本 {selected_version}:\n{filename}")

        except Exception as e:
            error_msg = f"打开文件时发生错误: {str(e)}"
            self.log(error_msg)
            QMessageBox.critical(self, "错误", error_msg)

    def save_file(self):
        """保存文件"""
        try:
            current_scene = cmds.file(query=True, sceneName=True)
            selected_version = self.version_combo.currentText()

            # 检查当前场景是否是项目文件
            if current_scene and self.base_name in os.path.basename(current_scene):
                current_version = self.extract_version_from_filename(os.path.basename(current_scene))
                if current_version == selected_version:
                    # 版本相同，直接保存
                    cmds.file(save=True)
                    self.log(f"文件保存成功: {os.path.basename(current_scene)}")
                    self.update_file_info()
                    QMessageBox.information(self, "成功", "文件保存成功！")
                    return

            # 另存为新版本
            self.save_as_version(selected_version)

        except Exception as e:
            error_msg = f"保存文件时发生错误: {str(e)}"
            self.log(error_msg)
            QMessageBox.critical(self, "错误", error_msg)

    def save_as_version(self, version):
        """另存为指定版本"""
        try:
            # 创建目录
            if not os.path.exists(self.save_dir):
                try:
                    os.makedirs(self.save_dir)
                    self.log(f"已创建目录: {self.save_dir}")
                except Exception as e:
                    self.log(f"无法创建目录: {str(e)}")
                    QMessageBox.critical(self, "错误", f"无法创建保存目录: {str(e)}")
                    return

            filename = self.generate_filename(version)
            file_path = os.path.join(self.save_dir, filename)

            # 检查覆盖确认
            if not self.check_file_exists_and_confirm(file_path, version):
                return

            self.log(f"正在保存为版本: {filename}")
            cmds.file(rename=file_path)
            cmds.file(save=True, type="mayaAscii")

            self.log(f"文件保存成功: {filename}")
            self.update_file_info()
            self.update_versions()
            QMessageBox.information(self, "保存成功", f"文件已保存为:\n{filename}")

        except Exception as e:
            error_msg = f"保存版本时发生错误: {str(e)}"
            self.log(error_msg)
            QMessageBox.critical(self, "保存错误", error_msg)


# 全局变量和启动函数
file_manager_window = None

def close_existing_windows():
    """关闭已存在的窗口"""
    try:
        for name in ["MayaFileManagerOptimizedWorkspaceControl", "MayaFileManagerOptimized"]:
            if cmds.workspaceControl(name, exists=True) if "Workspace" in name else cmds.window(name, exists=True):
                cmds.deleteUI(name)
                print(f"已删除现有的{'工作区控件' if 'Workspace' in name else '窗口'}")
    except Exception as e:
        print(f"清理现有窗口时发生错误: {str(e)}")

def show_file_manager():
    """显示文件管理器"""
    global file_manager_window
    try:
        close_existing_windows()
        if file_manager_window is not None:
            try:
                file_manager_window.close()
                file_manager_window.deleteLater()
            except:
                pass
            file_manager_window = None

        file_manager_window = MayaFileManager()
        file_manager_window.show(dockable=True)
        print("✅ Maya文件管理器界面已成功启动！")
        return file_manager_window
    except Exception as e:
        print(f"❌ 启动文件管理器时发生错误: {str(e)}")
        return None

def restart_file_manager():
    """重启文件管理器"""
    print("🔄 正在重启文件管理器...")
    close_existing_windows()
    return show_file_manager()

# 中文函数名
重启文件管理器 = restart_file_manager
关闭现有窗口 = close_existing_windows

# 立即启动
print("正在启动文件管理器界面...")
try:
    window = show_file_manager()
    if window:
        print("🎉 启动成功！文件管理器界面已显示。")
        print("如需重新打开，请运行: show_file_manager() 或 重启文件管理器()")
    else:
        print("❌ 启动失败")
except Exception as e:
    print(f"❌ 启动异常: {str(e)}")
    print("请确保在Maya 2022.5.1中运行此脚本")

print("=" * 50)
print("如果遇到问题，请运行: restart_file_manager() 或 重启文件管理器()")
print("=" * 50)
