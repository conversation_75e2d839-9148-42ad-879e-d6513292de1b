# -*- coding: utf-8 -*-
"""工具模块 | 作者: MMQ"""

import os
import re
import datetime
import subprocess
import platform

def parse_version(version_string):
    """解析版本号"""
    if version_string.startswith('v'):
        try:
            return int(version_string[1:])
        except:
            return 0
    return 0

def format_version(version_number):
    """格式化版本号"""
    return f"v{version_number:03d}"

def get_next_version(versions):
    """获取下一个版本号"""
    if not versions:
        return "v001"
    max_version = max([parse_version(v) for v in versions])
    return format_version(max_version + 1)

def get_latest_version(versions):
    """获取最新版本"""
    if not versions:
        return None
    return max(versions, key=parse_version)

def sort_versions(versions, reverse=True):
    """排序版本列表"""
    return sorted(versions, key=parse_version, reverse=reverse)

def get_file_size(file_path):
    """获取文件大小(MB)"""
    try:
        if os.path.exists(file_path):
            return os.path.getsize(file_path) / 1024 / 1024
        return 0.0
    except:
        return 0.0

def get_file_modified_time(file_path):
    """获取文件修改时间"""
    try:
        if os.path.exists(file_path):
            timestamp = os.path.getmtime(file_path)
            return datetime.datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M")
        return "未知"
    except:
        return "未知"

def get_file_info(file_path):
    """获取文件信息"""
    return {
        'exists': os.path.exists(file_path),
        'size': get_file_size(file_path),
        'modified': get_file_modified_time(file_path),
        'name': os.path.basename(file_path),
        'dir': os.path.dirname(file_path)
    }

def scan_versions(directory, base_name, extension):
    """扫描版本文件"""
    versions = []
    if not os.path.exists(directory):
        return versions

    try:
        pattern = rf"{re.escape(base_name)}_v(\d+){re.escape(extension)}"
        for filename in os.listdir(directory):
            match = re.match(pattern, filename)
            if match:
                version_num = int(match.group(1))
                versions.append(f"v{version_num:03d}")
    except:
        pass

    return sort_versions(versions)

def open_file_location(file_path):
    """打开文件位置"""
    try:
        directory = os.path.dirname(file_path)

        if not os.path.exists(directory):
            os.makedirs(directory)

        system = platform.system()
        if system == "Windows":
            if os.path.exists(file_path):
                subprocess.run(['explorer', '/select,', os.path.normpath(file_path)])
            else:
                subprocess.run(['explorer', os.path.normpath(directory)])
        elif system == "Darwin":
            if os.path.exists(file_path):
                subprocess.run(['open', '-R', file_path])
            else:
                subprocess.run(['open', directory])
        else:
            subprocess.run(['xdg-open', directory])

        return True
    except:
        return False

def ensure_directory(path):
    """确保目录存在"""
    try:
        if not os.path.exists(path):
            os.makedirs(path)
        return True
    except:
        return False

def get_version_color(version, latest_version, existing_versions):
    """获取版本颜色"""
    if version == latest_version:
        return (46, 204, 113)  # 绿色
    elif version in existing_versions:
        return (231, 76, 60)   # 红色
    else:
        return (255, 255, 255) # 白色

def get_status_icon(version, latest_version, existing_versions):
    """获取状态图标"""
    if version == latest_version:
        return "🟢"
    elif version in existing_versions:
        return "🔴"
    else:
        return "⚪"

def get_timestamp():
    """获取时间戳"""
    return datetime.datetime.now().strftime("%H:%M:%S")
