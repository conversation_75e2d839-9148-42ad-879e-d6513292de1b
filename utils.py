# -*- coding: utf-8 -*-
"""通用工具类 - 版本计算和文件操作 | 作者: MMQ"""

import os
import re
import datetime
import subprocess
import platform
from typing import List, Dict, Optional, Tuple

class VersionCalculator:
    """版本计算器"""
    
    @staticmethod
    def parse_version(version_string: str) -> int:
        """解析版本字符串为数字"""
        if version_string.startswith('v'):
            try:
                return int(version_string[1:])
            except ValueError:
                return 0
        return 0
    
    @staticmethod
    def format_version(version_number: int) -> str:
        """格式化版本号为字符串"""
        return f"v{version_number:03d}"
    
    @staticmethod
    def get_next_version(versions: List[str]) -> str:
        """获取下一个版本号"""
        if not versions:
            return "v001"
        
        max_version = max([VersionCalculator.parse_version(v) for v in versions])
        return VersionCalculator.format_version(max_version + 1)
    
    @staticmethod
    def get_latest_version(versions: List[str]) -> Optional[str]:
        """获取最新版本"""
        if not versions:
            return None
        
        return max(versions, key=VersionCalculator.parse_version)
    
    @staticmethod
    def sort_versions(versions: List[str], reverse: bool = True) -> List[str]:
        """排序版本列表"""
        return sorted(versions, key=VersionCalculator.parse_version, reverse=reverse)
    
    @staticmethod
    def generate_version_range(start_version: str, count: int) -> List[str]:
        """生成版本范围"""
        start_num = VersionCalculator.parse_version(start_version)
        return [VersionCalculator.format_version(start_num + i) for i in range(count)]

class FileUtils:
    """文件工具类"""
    
    @staticmethod
    def get_file_size(file_path: str) -> float:
        """获取文件大小(MB)"""
        try:
            if os.path.exists(file_path):
                return os.path.getsize(file_path) / 1024 / 1024
            return 0.0
        except:
            return 0.0
    
    @staticmethod
    def get_file_modified_time(file_path: str) -> str:
        """获取文件修改时间"""
        try:
            if os.path.exists(file_path):
                timestamp = os.path.getmtime(file_path)
                return datetime.datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M")
            return "未知"
        except:
            return "未知"
    
    @staticmethod
    def get_file_info(file_path: str) -> Dict:
        """获取文件详细信息"""
        return {
            'exists': os.path.exists(file_path),
            'size': FileUtils.get_file_size(file_path),
            'modified': FileUtils.get_file_modified_time(file_path),
            'name': os.path.basename(file_path),
            'dir': os.path.dirname(file_path)
        }
    
    @staticmethod
    def scan_versions(directory: str, base_name: str, extension: str) -> List[str]:
        """扫描目录中的版本文件"""
        versions = []
        if not os.path.exists(directory):
            return versions
        
        try:
            pattern = rf"{re.escape(base_name)}_v(\d+){re.escape(extension)}"
            for filename in os.listdir(directory):
                match = re.match(pattern, filename)
                if match:
                    version_num = int(match.group(1))
                    versions.append(f"v{version_num:03d}")
        except Exception as e:
            print(f"扫描版本失败: {e}")
        
        return VersionCalculator.sort_versions(versions)
    
    @staticmethod
    def open_file_location(file_path: str) -> bool:
        """在系统文件管理器中打开文件位置"""
        try:
            directory = os.path.dirname(file_path)
            
            # 确保目录存在
            if not os.path.exists(directory):
                os.makedirs(directory)
            
            system = platform.system()
            if system == "Windows":
                if os.path.exists(file_path):
                    subprocess.run(['explorer', '/select,', os.path.normpath(file_path)])
                else:
                    subprocess.run(['explorer', os.path.normpath(directory)])
            elif system == "Darwin":  # macOS
                if os.path.exists(file_path):
                    subprocess.run(['open', '-R', file_path])
                else:
                    subprocess.run(['open', directory])
            else:  # Linux
                subprocess.run(['xdg-open', directory])
            
            return True
        except Exception as e:
            print(f"打开文件位置失败: {e}")
            return False

class PathUtils:
    """路径工具类"""
    
    @staticmethod
    def normalize_path(path: str) -> str:
        """标准化路径"""
        return os.path.normpath(os.path.expanduser(path))
    
    @staticmethod
    def ensure_directory(path: str) -> bool:
        """确保目录存在"""
        try:
            if not os.path.exists(path):
                os.makedirs(path)
            return True
        except Exception as e:
            print(f"创建目录失败: {e}")
            return False
    
    @staticmethod
    def get_relative_path(file_path: str, base_path: str) -> str:
        """获取相对路径"""
        try:
            return os.path.relpath(file_path, base_path)
        except:
            return file_path
    
    @staticmethod
    def join_path(*args) -> str:
        """安全的路径连接"""
        return os.path.normpath(os.path.join(*args))

class ColorUtils:
    """颜色工具类"""
    
    # 预定义颜色
    COLORS = {
        'latest': (46, 204, 113),    # 绿色 - 最新版本
        'history': (231, 76, 60),    # 红色 - 历史版本
        'new': (255, 255, 255),      # 白色 - 新版本
        'warning': (255, 193, 7),    # 黄色 - 警告
        'info': (52, 152, 219),      # 蓝色 - 信息
        'success': (40, 167, 69),    # 绿色 - 成功
        'error': (220, 53, 69)       # 红色 - 错误
    }
    
    @staticmethod
    def get_version_color(version: str, latest_version: str, existing_versions: List[str]) -> Tuple[int, int, int]:
        """根据版本状态获取颜色"""
        if version == latest_version:
            return ColorUtils.COLORS['latest']
        elif version in existing_versions:
            return ColorUtils.COLORS['history']
        else:
            return ColorUtils.COLORS['new']
    
    @staticmethod
    def rgb_to_hex(r: int, g: int, b: int) -> str:
        """RGB转十六进制"""
        return f"#{r:02x}{g:02x}{b:02x}"
    
    @staticmethod
    def get_status_icon(version: str, latest_version: str, existing_versions: List[str]) -> str:
        """获取状态图标"""
        if version == latest_version:
            return "🟢"
        elif version in existing_versions:
            return "🔴"
        else:
            return "⚪"

class TimeUtils:
    """时间工具类"""
    
    @staticmethod
    def get_timestamp() -> str:
        """获取当前时间戳"""
        return datetime.datetime.now().strftime("%H:%M:%S")
    
    @staticmethod
    def get_date_string() -> str:
        """获取日期字符串"""
        return datetime.datetime.now().strftime("%Y-%m-%d")
    
    @staticmethod
    def get_datetime_string() -> str:
        """获取日期时间字符串"""
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    @staticmethod
    def format_duration(seconds: float) -> str:
        """格式化持续时间"""
        if seconds < 60:
            return f"{seconds:.1f}秒"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.1f}分钟"
        else:
            hours = seconds / 3600
            return f"{hours:.1f}小时"

class ValidationUtils:
    """验证工具类"""
    
    @staticmethod
    def is_valid_version(version: str) -> bool:
        """验证版本号格式"""
        pattern = r'^v\d{3}$'
        return bool(re.match(pattern, version))
    
    @staticmethod
    def is_valid_filename(filename: str) -> bool:
        """验证文件名"""
        invalid_chars = '<>:"/\\|?*'
        return not any(char in filename for char in invalid_chars)
    
    @staticmethod
    def is_valid_path(path: str) -> bool:
        """验证路径"""
        try:
            os.path.normpath(path)
            return True
        except:
            return False
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """清理文件名"""
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        return filename

# 便捷函数
def calculate_next_version(versions: List[str]) -> str:
    """计算下一个版本号"""
    return VersionCalculator.get_next_version(versions)

def get_file_size_mb(file_path: str) -> float:
    """获取文件大小(MB)"""
    return FileUtils.get_file_size(file_path)

def open_folder(file_path: str) -> bool:
    """打开文件夹"""
    return FileUtils.open_file_location(file_path)

def ensure_dir(path: str) -> bool:
    """确保目录存在"""
    return PathUtils.ensure_directory(path)
