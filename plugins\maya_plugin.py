# -*- coding: utf-8 -*-
"""Maya插件 | 作者: MMQ"""

import os
from plugins.base_plugin import PluginBase

class MayaPlugin(PluginBase):
    """Maya插件"""
    
    def __init__(self):
        self.available = False
        self.cmds = None
        
        try:
            import maya.cmds as cmds
            self.cmds = cmds
            self.available = True
        except ImportError:
            pass
    
    def get_name(self):
        """返回插件名称"""
        if self.available:
            try:
                version = self.cmds.about(version=True)
                return f"Maya {version}"
            except:
                return "Maya"
        return "Maya (不可用)"
    
    def get_file_extension(self):
        """返回文件扩展名"""
        return ".ma"
    
    def open_file(self, file_path):
        """打开Maya文件"""
        if not self.available:
            return False
        
        try:
            self.cmds.file(file_path, open=True, force=True)
            return True
        except Exception as e:
            print(f"打开Maya文件失败: {e}")
            return False
    
    def save_file(self, file_path):
        """保存Maya文件"""
        if not self.available:
            return False
        
        try:
            self.cmds.file(rename=file_path)
            self.cmds.file(save=True, type="mayaAscii")
            return True
        except Exception as e:
            print(f"保存Maya文件失败: {e}")
            return False
    
    def check_unsaved_changes(self):
        """检查是否有未保存的更改"""
        if not self.available:
            return False
        
        try:
            return self.cmds.file(query=True, modified=True)
        except:
            return False
    
    def get_current_file_info(self):
        """获取当前文件信息"""
        if not self.available:
            return {
                'file': None,
                'modified': False,
                'exists': False
            }
        
        try:
            current_file = self.cmds.file(query=True, sceneName=True)
            modified = self.cmds.file(query=True, modified=True)
            
            return {
                'file': current_file,
                'modified': modified,
                'exists': bool(current_file and os.path.exists(current_file)),
                'name': os.path.basename(current_file) if current_file else "untitled"
            }
        except Exception as e:
            print(f"获取Maya文件信息失败: {e}")
            return {
                'file': None,
                'modified': False,
                'exists': False
            }
    

