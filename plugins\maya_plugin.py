# -*- coding: utf-8 -*-
"""Maya插件 | 作者: MMQ"""

import os
from abc import ABC, abstractmethod

class PluginBase(ABC):
    """插件基类"""
    
    @abstractmethod
    def get_name(self):
        """返回插件名称"""
        pass
    
    @abstractmethod
    def get_file_extension(self):
        """返回文件扩展名"""
        pass
    
    @abstractmethod
    def open_file(self, file_path):
        """打开文件"""
        pass
    
    @abstractmethod
    def save_file(self, file_path):
        """保存文件"""
        pass
    
    @abstractmethod
    def check_unsaved_changes(self):
        """检查未保存的更改"""
        pass
    
    @abstractmethod
    def get_current_file_info(self):
        """获取当前文件信息"""
        pass

class MayaPlugin(PluginBase):
    """Maya插件"""
    
    def __init__(self):
        self.available = False
        self.cmds = None
        
        try:
            import maya.cmds as cmds
            self.cmds = cmds
            self.available = True
        except ImportError:
            pass
    
    def get_name(self):
        """返回插件名称"""
        if self.available:
            try:
                version = self.cmds.about(version=True)
                return f"Maya {version}"
            except:
                return "Maya"
        return "Maya (不可用)"
    
    def get_file_extension(self):
        """返回文件扩展名"""
        return ".ma"
    
    def open_file(self, file_path):
        """打开Maya文件"""
        if not self.available:
            return False
        
        try:
            self.cmds.file(file_path, open=True, force=True)
            return True
        except Exception as e:
            print(f"打开Maya文件失败: {e}")
            return False
    
    def save_file(self, file_path):
        """保存Maya文件"""
        if not self.available:
            return False
        
        try:
            self.cmds.file(rename=file_path)
            self.cmds.file(save=True, type="mayaAscii")
            return True
        except Exception as e:
            print(f"保存Maya文件失败: {e}")
            return False
    
    def check_unsaved_changes(self):
        """检查是否有未保存的更改"""
        if not self.available:
            return False
        
        try:
            return self.cmds.file(query=True, modified=True)
        except:
            return False
    
    def get_current_file_info(self):
        """获取当前文件信息"""
        if not self.available:
            return {
                'file': None,
                'modified': False,
                'exists': False
            }
        
        try:
            current_file = self.cmds.file(query=True, sceneName=True)
            modified = self.cmds.file(query=True, modified=True)
            
            return {
                'file': current_file,
                'modified': modified,
                'exists': bool(current_file and os.path.exists(current_file)),
                'name': os.path.basename(current_file) if current_file else "untitled"
            }
        except Exception as e:
            print(f"获取Maya文件信息失败: {e}")
            return {
                'file': None,
                'modified': False,
                'exists': False
            }
    
    def new_scene(self):
        """新建场景"""
        if not self.available:
            return False
        
        try:
            self.cmds.file(new=True, force=True)
            return True
        except Exception as e:
            print(f"新建Maya场景失败: {e}")
            return False
    
    def import_file(self, file_path):
        """导入文件"""
        if not self.available:
            return False
        
        try:
            self.cmds.file(file_path, i=True)
            return True
        except Exception as e:
            print(f"导入Maya文件失败: {e}")
            return False
    
    def export_selection(self, file_path):
        """导出选中对象"""
        if not self.available:
            return False
        
        try:
            self.cmds.file(file_path, exportSelected=True, type="mayaAscii")
            return True
        except Exception as e:
            print(f"导出Maya文件失败: {e}")
            return False
    
    def get_scene_info(self):
        """获取场景信息"""
        if not self.available:
            return {}
        
        try:
            info = {
                'objects_count': len(self.cmds.ls(dag=True, long=True)),
                'selected_count': len(self.cmds.ls(selection=True)),
                'frame_range': (
                    self.cmds.playbackOptions(query=True, minTime=True),
                    self.cmds.playbackOptions(query=True, maxTime=True)
                ),
                'current_frame': self.cmds.currentTime(query=True),
                'units': {
                    'linear': self.cmds.currentUnit(query=True, linear=True),
                    'angular': self.cmds.currentUnit(query=True, angle=True),
                    'time': self.cmds.currentUnit(query=True, time=True)
                }
            }
            return info
        except Exception as e:
            print(f"获取Maya场景信息失败: {e}")
            return {}
    
    def set_project(self, project_path):
        """设置Maya项目"""
        if not self.available:
            return False
        
        try:
            self.cmds.workspace(project_path, openWorkspace=True)
            return True
        except Exception as e:
            print(f"设置Maya项目失败: {e}")
            return False
    
    def get_project_path(self):
        """获取当前项目路径"""
        if not self.available:
            return None
        
        try:
            return self.cmds.workspace(query=True, rootDirectory=True)
        except Exception as e:
            print(f"获取Maya项目路径失败: {e}")
            return None
    
    def is_available(self):
        """检查插件是否可用"""
        return self.available
    
    def get_supported_formats(self):
        """获取支持的文件格式"""
        return ['.ma', '.mb']
    
    def validate_file(self, file_path):
        """验证文件是否有效"""
        if not os.path.exists(file_path):
            return False
        
        ext = os.path.splitext(file_path)[1].lower()
        return ext in self.get_supported_formats()
