# -*- coding: utf-8 -*-
"""
Maya用户启动脚本
此文件在Maya启动时自动执行

将此文件复制到Maya的scripts目录中：
Windows: ~/Documents/maya/[version]/scripts/
macOS: ~/Library/Preferences/Autodesk/maya/[version]/scripts/
Linux: ~/maya/[version]/scripts/
"""

# Maya文件管理器插件自动加载
try:
    import maya_file_manager
    maya_file_manager.initialize_plugin()
    print("Maya文件管理器插件已自动加载")
except Exception as e:
    print(f"加载Maya文件管理器插件时发生错误: {str(e)}")

# 您可以在此处添加其他启动脚本
# 例如：
# import my_other_plugin
# my_other_plugin.initialize()
