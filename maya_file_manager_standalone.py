# -*- coding: utf-8 -*-
"""
Maya文件管理器 - 独立运行版本
作者: MMQ
版本: 1.0
兼容性: Maya 2022+

这是一个可以直接在Maya脚本编辑器中运行的完整文件管理器
包含图形界面和完整的文件操作功能
"""

import os
import sys

# 检查Maya环境
try:
    import maya.cmds as cmds
    import maya.mel as mel
    from maya.app.general.mayaMixin import MayaQWidgetDockableMixin
    MAYA_AVAILABLE = True
    print("✓ Maya环境检测成功")
except ImportError:
    print("✗ 错误：请在Maya环境中运行此脚本")
    MAYA_AVAILABLE = False

# 检查PySide2
try:
    from PySide2.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                                   QLabel, QFileDialog, QMessageBox, QTextEdit,
                                   QFrame, QApplication)
    from PySide2.QtCore import Qt, Signal
    from PySide2.QtGui import QFont
    PYSIDE2_AVAILABLE = True
    print("✓ PySide2模块检测成功")
except ImportError:
    print("✗ 错误：PySide2模块未找到，请确保Maya版本支持PySide2")
    PYSIDE2_AVAILABLE = False

if not (MAYA_AVAILABLE and PYSIDE2_AVAILABLE):
    print("环境检查失败，无法继续运行")
    sys.exit(1)

class MayaFileManager(MayaQWidgetDockableMixin, QWidget):
    """Maya文件管理器主界面类"""

    # 定义信号
    file_opened = Signal(str)
    file_saved = Signal(str)

    def __init__(self, parent=None):
        """初始化文件管理器界面"""
        super(MayaFileManager, self).__init__(parent)

        # 设置窗口属性
        self.setWindowTitle("Maya文件管理器 v1.0")
        self.setMinimumSize(450, 400)
        self.setObjectName("MayaFileManagerStandalone")

        # 支持的文件格式
        self.supported_formats = {
            'Maya ASCII (*.ma)': '*.ma',
            'Maya Binary (*.mb)': '*.mb',
            'All Maya Files (*.ma *.mb)': '*.ma *.mb',
            'All Files (*.*)': '*.*'
        }

        # 创建界面
        self.create_ui()
        self.setup_connections()

        # 记录初始化信息
        self.log_message("文件管理器界面初始化完成")
        self.log_message(f"Maya版本: {cmds.about(version=True)}")

    def create_ui(self):
        """创建用户界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # 标题区域
        title_label = QLabel("Maya文件管理器")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)

        # 版本信息
        version_label = QLabel("适用于Maya 2022+ | 作者: MMQ")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet("color: #7f8c8d; font-size: 11px; margin-bottom: 10px;")
        main_layout.addWidget(version_label)

        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        line.setStyleSheet("color: #bdc3c7;")
        main_layout.addWidget(line)

        # 按钮区域
        button_layout = QVBoxLayout()
        button_layout.setSpacing(12)

        # 打开文件按钮
        self.open_button = QPushButton("🗂️ 打开Maya文件")
        self.open_button.setMinimumHeight(45)
        self.open_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 13px;
                font-weight: bold;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        button_layout.addWidget(self.open_button)

        # 保存文件按钮
        self.save_button = QPushButton("💾 保存Maya文件")
        self.save_button.setMinimumHeight(45)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 13px;
                font-weight: bold;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        button_layout.addWidget(self.save_button)

        # 另存为按钮
        self.save_as_button = QPushButton("📁 另存为...")
        self.save_as_button.setMinimumHeight(45)
        self.save_as_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 13px;
                font-weight: bold;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
            QPushButton:pressed {
                background-color: #d35400;
            }
        """)
        button_layout.addWidget(self.save_as_button)

        main_layout.addLayout(button_layout)

        # 当前文件信息
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 8px;
            }
        """)
        info_layout = QVBoxLayout(info_frame)

        current_file_label = QLabel("当前文件信息:")
        current_file_label.setFont(QFont("Arial", 10, QFont.Bold))
        current_file_label.setStyleSheet("color: #495057;")
        info_layout.addWidget(current_file_label)

        self.current_file_info = QLabel("未打开任何文件")
        self.current_file_info.setStyleSheet("color: #6c757d; font-size: 11px; padding: 5px;")
        self.current_file_info.setWordWrap(True)
        info_layout.addWidget(self.current_file_info)

        main_layout.addWidget(info_frame)

        # 状态信息区域
        status_label = QLabel("操作日志:")
        status_label.setFont(QFont("Arial", 10, QFont.Bold))
        status_label.setStyleSheet("color: #495057;")
        main_layout.addWidget(status_label)

        # 状态文本框
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(150)
        self.status_text.setReadOnly(True)
        self.status_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
                color: #495057;
            }
        """)
        main_layout.addWidget(self.status_text)

        # 底部提示信息
        tip_label = QLabel("💡 提示: 支持 .ma 和 .mb 格式的Maya文件")
        tip_label.setStyleSheet("""
            color: #6c757d;
            font-size: 11px;
            padding: 8px;
            background-color: #e9ecef;
            border-radius: 4px;
        """)
        tip_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(tip_label)

        # 更新当前文件信息
        self.update_current_file_info()

    def setup_connections(self):
        """设置信号连接"""
        self.open_button.clicked.connect(self.open_file_dialog)
        self.save_button.clicked.connect(self.save_current_file)
        self.save_as_button.clicked.connect(self.save_file_dialog)

        # 连接自定义信号
        self.file_opened.connect(self.on_file_opened)
        self.file_saved.connect(self.on_file_saved)

    def log_message(self, message, level="INFO"):
        """在状态文本框中记录消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")

        # 根据级别设置图标
        if level == "ERROR":
            icon = "❌"
        elif level == "WARNING":
            icon = "⚠️"
        elif level == "SUCCESS":
            icon = "✅"
        else:
            icon = "ℹ️"

        formatted_message = f"[{timestamp}] {icon} {message}"
        self.status_text.append(formatted_message)

        # 自动滚动到底部
        scrollbar = self.status_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

        # 同时在Maya脚本编辑器中输出
        print(f"[文件管理器] {message}")

    def update_current_file_info(self):
        """更新当前文件信息显示"""
        try:
            current_scene = cmds.file(query=True, sceneName=True)
            if current_scene:
                file_name = os.path.basename(current_scene)
                file_dir = os.path.dirname(current_scene)
                file_size = os.path.getsize(current_scene) if os.path.exists(current_scene) else 0
                size_mb = file_size / (1024 * 1024)

                info_text = f"文件名: {file_name}\n路径: {file_dir}\n大小: {size_mb:.2f} MB"
                self.current_file_info.setText(info_text)
            else:
                self.current_file_info.setText("当前场景: 新建场景 (未保存)")
        except Exception as e:
            self.current_file_info.setText(f"获取文件信息失败: {str(e)}")

    def open_file_dialog(self):
        """打开文件对话框"""
        try:
            self.log_message("正在打开文件选择对话框...")

            # 获取当前Maya工作目录
            try:
                current_workspace = cmds.workspace(query=True, rootDirectory=True)
            except:
                current_workspace = os.path.expanduser("~")

            # 构建文件过滤器
            file_filter = ";;".join(self.supported_formats.keys())

            # 打开文件对话框
            file_path, selected_filter = QFileDialog.getOpenFileName(
                self,
                "选择Maya文件",
                current_workspace,
                file_filter
            )

            if file_path:
                self.open_maya_file(file_path)
            else:
                self.log_message("用户取消了文件选择", "WARNING")

        except Exception as e:
            error_msg = f"打开文件对话框时发生错误: {str(e)}"
            self.log_message(error_msg, "ERROR")
            self.show_error_message("错误", error_msg)

    def save_current_file(self):
        """保存当前文件"""
        try:
            # 检查当前场景是否有文件名
            current_scene = cmds.file(query=True, sceneName=True)
            if current_scene:
                # 直接保存当前文件
                self.log_message(f"正在保存当前文件: {os.path.basename(current_scene)}")
                cmds.file(save=True)
                self.log_message("文件保存成功", "SUCCESS")
                self.update_current_file_info()
                self.file_saved.emit(current_scene)
            else:
                # 如果没有文件名，调用另存为
                self.log_message("当前场景未命名，将打开另存为对话框", "WARNING")
                self.save_file_dialog()

        except Exception as e:
            error_msg = f"保存文件时发生错误: {str(e)}"
            self.log_message(error_msg, "ERROR")
            self.show_error_message("保存错误", error_msg)

    def save_file_dialog(self):
        """保存文件对话框（另存为）"""
        try:
            self.log_message("正在打开文件保存对话框...")

            # 获取当前Maya工作目录
            try:
                current_workspace = cmds.workspace(query=True, rootDirectory=True)
            except:
                current_workspace = os.path.expanduser("~")

            # 获取当前场景名称作为默认文件名
            current_scene = cmds.file(query=True, sceneName=True)
            if current_scene:
                default_name = os.path.splitext(os.path.basename(current_scene))[0]
            else:
                default_name = "untitled"

            default_path = os.path.join(current_workspace, default_name + ".ma")

            # 构建文件过滤器（保存时只显示Maya格式）
            save_filters = {
                'Maya ASCII (*.ma)': '*.ma',
                'Maya Binary (*.mb)': '*.mb'
            }
            file_filter = ";;".join(save_filters.keys())

            # 打开保存对话框
            file_path, selected_filter = QFileDialog.getSaveFileName(
                self,
                "保存Maya文件",
                default_path,
                file_filter
            )

            if file_path:
                self.save_maya_file(file_path, selected_filter)
            else:
                self.log_message("用户取消了文件保存", "WARNING")

        except Exception as e:
            error_msg = f"打开保存对话框时发生错误: {str(e)}"
            self.log_message(error_msg, "ERROR")
            self.show_error_message("错误", error_msg)

    def open_maya_file(self, file_path):
        """打开Maya文件"""
        try:
            # 验证文件路径
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")

            # 验证文件格式
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in ['.ma', '.mb']:
                raise ValueError(f"不支持的文件格式: {file_ext}")

            self.log_message(f"正在打开文件: {os.path.basename(file_path)}")

            # 检查当前场景是否有未保存的更改
            if cmds.file(query=True, modified=True):
                reply = QMessageBox.question(
                    self,
                    "确认操作",
                    "当前场景有未保存的更改。\n是否继续打开新文件？\n\n点击 Yes 继续，点击 No 取消操作。",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                if reply == QMessageBox.No:
                    self.log_message("用户取消了文件打开操作", "WARNING")
                    return

            # 打开文件
            cmds.file(file_path, open=True, force=True)

            success_msg = f"成功打开文件: {os.path.basename(file_path)}"
            self.log_message(success_msg, "SUCCESS")
            self.show_success_message("操作成功", success_msg)

            # 更新界面信息
            self.update_current_file_info()

            # 发射信号
            self.file_opened.emit(file_path)

        except FileNotFoundError as e:
            error_msg = f"文件未找到: {str(e)}"
            self.log_message(error_msg, "ERROR")
            self.show_error_message("文件错误", error_msg)

        except ValueError as e:
            error_msg = f"文件格式错误: {str(e)}"
            self.log_message(error_msg, "ERROR")
            self.show_error_message("格式错误", error_msg)

        except Exception as e:
            error_msg = f"打开文件时发生未知错误: {str(e)}"
            self.log_message(error_msg, "ERROR")
            self.show_error_message("错误", error_msg)

    def save_maya_file(self, file_path, selected_filter):
        """保存Maya文件"""
        try:
            # 确定文件类型
            if "ASCII" in selected_filter:
                file_type = "mayaAscii"
                if not file_path.endswith('.ma'):
                    file_path += '.ma'
            else:
                file_type = "mayaBinary"
                if not file_path.endswith('.mb'):
                    file_path += '.mb'

            self.log_message(f"正在保存文件: {os.path.basename(file_path)} (格式: {file_type})")

            # 检查目录权限
            directory = os.path.dirname(file_path)
            if not os.access(directory, os.W_OK):
                raise PermissionError(f"没有写入权限: {directory}")

            # 保存文件
            cmds.file(rename=file_path)
            cmds.file(save=True, type=file_type)

            success_msg = f"成功保存文件: {os.path.basename(file_path)}"
            self.log_message(success_msg, "SUCCESS")
            self.show_success_message("保存成功", success_msg)

            # 更新界面信息
            self.update_current_file_info()

            # 发射信号
            self.file_saved.emit(file_path)

        except PermissionError as e:
            error_msg = f"权限错误: {str(e)}"
            self.log_message(error_msg, "ERROR")
            self.show_error_message("权限错误", error_msg)

        except Exception as e:
            error_msg = f"保存文件时发生错误: {str(e)}"
            self.log_message(error_msg, "ERROR")
            self.show_error_message("保存错误", error_msg)

    def show_success_message(self, title, message):
        """显示成功消息"""
        msg_box = QMessageBox(self)
        msg_box.setIcon(QMessageBox.Information)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setStyleSheet("""
            QMessageBox {
                background-color: white;
            }
            QMessageBox QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QMessageBox QPushButton:hover {
                background-color: #229954;
            }
        """)
        msg_box.exec_()

    def show_error_message(self, title, message):
        """显示错误消息"""
        msg_box = QMessageBox(self)
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setStyleSheet("""
            QMessageBox {
                background-color: white;
            }
            QMessageBox QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QMessageBox QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        msg_box.exec_()

    def on_file_opened(self, file_path):
        """文件打开后的回调"""
        self.log_message(f"文件打开事件触发: {os.path.basename(file_path)}")

    def on_file_saved(self, file_path):
        """文件保存后的回调"""
        self.log_message(f"文件保存事件触发: {os.path.basename(file_path)}")

    def closeEvent(self, event):
        """窗口关闭事件"""
        self.log_message("文件管理器窗口正在关闭", "WARNING")
        event.accept()


# 全局变量存储窗口实例
file_manager_window = None


def show_file_manager():
    """显示文件管理器窗口"""
    global file_manager_window

    try:
        # 如果窗口已存在，则显示它
        if file_manager_window is not None:
            file_manager_window.show()
            file_manager_window.raise_()
            file_manager_window.activateWindow()
            print("文件管理器窗口已显示")
            return file_manager_window

        # 创建新窗口
        file_manager_window = MayaFileManager()
        file_manager_window.show(dockable=True)

        print("✅ 文件管理器窗口已创建并显示")
        print("🎯 您现在可以使用图形界面来打开和保存Maya文件了！")
        return file_manager_window

    except Exception as e:
        error_msg = f"显示文件管理器时发生错误: {str(e)}"
        print(f"❌ {error_msg}")
        cmds.warning(error_msg)
        return None


def close_file_manager():
    """关闭文件管理器窗口"""
    global file_manager_window

    try:
        if file_manager_window is not None:
            file_manager_window.close()
            file_manager_window = None
            print("✅ 文件管理器窗口已关闭")
        else:
            print("⚠️ 文件管理器窗口未打开")

    except Exception as e:
        error_msg = f"关闭文件管理器时发生错误: {str(e)}"
        print(f"❌ {error_msg}")
        cmds.warning(error_msg)


def restart_file_manager():
    """重启文件管理器"""
    print("🔄 正在重启文件管理器...")
    close_file_manager()
    return show_file_manager()


# 中文函数名，方便在Maya脚本编辑器中使用
def 打开文件管理器():
    """中文函数名 - 打开文件管理器"""
    return show_file_manager()


def 关闭文件管理器():
    """中文函数名 - 关闭文件管理器"""
    return close_file_manager()


def 重启文件管理器():
    """中文函数名 - 重启文件管理器"""
    return restart_file_manager()


def 使用说明():
    """显示使用说明"""
    print("=" * 60)
    print("🎯 Maya文件管理器 v1.0 - 使用说明")
    print("=" * 60)
    print()
    print("📋 功能介绍:")
    print("   • 图形化界面打开和保存Maya文件")
    print("   • 支持 .ma (ASCII) 和 .mb (Binary) 格式")
    print("   • 实时显示当前文件信息")
    print("   • 详细的操作日志记录")
    print("   • 适配Maya 2022.5.1版本")
    print()
    print("🚀 快速启动:")
    print("   show_file_manager()     # 打开文件管理器")
    print("   打开文件管理器()         # 中文版本")
    print()
    print("🔧 其他命令:")
    print("   close_file_manager()    # 关闭文件管理器")
    print("   restart_file_manager()  # 重启文件管理器")
    print("   关闭文件管理器()         # 中文版本")
    print("   重启文件管理器()         # 中文版本")
    print()
    print("💡 使用提示:")
    print("   1. 运行 show_file_manager() 启动图形界面")
    print("   2. 点击按钮进行文件操作")
    print("   3. 查看操作日志了解详细信息")
    print("   4. 窗口支持停靠到Maya界面")
    print()
    print("=" * 60)


# 主执行部分 - 直接启动界面
if __name__ == "__main__":
    # 检查环境
    if not (MAYA_AVAILABLE and PYSIDE2_AVAILABLE):
        print("❌ 环境检查失败，无法运行文件管理器")
        print("请确保在Maya 2022+环境中运行此脚本")
    else:
        # 显示欢迎信息
        print("🎉 Maya文件管理器正在启动...")
        print("=" * 50)

        # 直接启动文件管理器界面
        try:
            window = show_file_manager()
            if window:
                print("✅ 文件管理器界面已成功启动！")
                print("📝 如需帮助，请输入 使用说明()")
            else:
                print("❌ 启动失败，请检查Maya环境")
        except Exception as e:
            print(f"❌ 启动时发生错误: {str(e)}")
            print("请尝试手动运行: show_file_manager()")

# 如果是通过exec()运行，也自动启动
else:
    # 检查环境并自动启动
    if MAYA_AVAILABLE and PYSIDE2_AVAILABLE:
        print("🚀 正在自动启动Maya文件管理器...")
        try:
            window = show_file_manager()
            if window:
                print("✅ 文件管理器界面已启动！")
            else:
                print("❌ 自动启动失败")
        except Exception as e:
            print(f"❌ 自动启动时发生错误: {str(e)}")
    else:
        print("❌ Maya环境检查失败")


# 便捷启动 - 如果用户直接运行脚本，自动启动界面
def auto_start():
    """自动启动文件管理器"""
    if MAYA_AVAILABLE and PYSIDE2_AVAILABLE:
        return show_file_manager()
    else:
        print("❌ 无法自动启动：环境检查失败")
        return None


# 便捷启动 - 如果用户直接运行脚本，自动启动界面
def auto_start():
    """自动启动文件管理器"""
    if MAYA_AVAILABLE and PYSIDE2_AVAILABLE:
        return show_file_manager()
    else:
        print("❌ 无法自动启动：环境检查失败")
        return None


# 导出主要函数供外部调用
__all__ = [
    'show_file_manager', 'close_file_manager', 'restart_file_manager',
    '打开文件管理器', '关闭文件管理器', '重启文件管理器', '使用说明',
    'MayaFileManager', 'auto_start'
]


# 强制启动 - 无论如何都尝试启动界面
print("🔥 强制启动Maya文件管理器...")
if MAYA_AVAILABLE and PYSIDE2_AVAILABLE:
    try:
        window = show_file_manager()
        if window:
            print("🎉 Maya文件管理器界面已成功启动！")
            print("🎯 您现在可以使用图形界面来打开和保存Maya文件了！")
        else:
            print("❌ 启动失败")
    except Exception as e:
        print(f"❌ 启动错误: {str(e)}")
        print("请检查Maya环境和PySide2模块")
else:
    print("❌ 环境检查失败，请在Maya中运行此脚本")
