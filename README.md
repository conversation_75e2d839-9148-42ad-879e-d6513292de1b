# Maya文件管理器插件

一个功能强大的Maya文件管理插件，提供用户友好的文件打开和保存界面。

## 功能特性

- 🎨 **现代化UI设计**: 使用PySide2构建的美观界面
- 📁 **文件管理**: 支持Maya ASCII (.ma) 和 Maya Binary (.mb) 格式
- 🔧 **Maya集成**: 完全集成到Maya的UI系统中，支持停靠面板
- ⚠️ **错误处理**: 完善的错误处理和用户反馈机制
- 📝 **日志记录**: 详细的操作日志和状态信息
- 🔄 **自动加载**: 支持Maya启动时自动加载

## 系统要求

- Maya 2020 或更高版本
- PySide2 (Maya 2020+ 内置)
- Windows / macOS / Linux

## 安装方法

### 方法一：自动安装（推荐）

1. 下载所有插件文件到一个文件夹
2. 在命令行中运行安装脚本：
   ```bash
   python install_plugin.py
   ```
3. 重启Maya

### 方法二：手动安装

1. 找到Maya的scripts目录：
   - **Windows**: `~/Documents/maya/[版本]/scripts/`
   - **macOS**: `~/Library/Preferences/Autodesk/maya/[版本]/scripts/`
   - **Linux**: `~/maya/[版本]/scripts/`

2. 将 `maya_file_manager.py` 复制到scripts目录

3. 将 `userSetup.py` 复制到scripts目录（如果已存在userSetup.py，请将内容合并）

4. 重启Maya

## 使用方法

### 启动插件

插件安装后会自动加载。您可以通过以下方式访问：

1. **菜单栏**: Maya主菜单栏中的"文件管理器"菜单
2. **脚本编辑器**: 运行以下Python代码
   ```python
   import maya_file_manager
   maya_file_manager.show_file_manager()
   ```

### 界面功能

#### 打开文件
- 点击"打开文件"按钮
- 在文件对话框中选择Maya文件 (.ma 或 .mb)
- 插件会检查当前场景是否有未保存的更改
- 确认后打开选定的文件

#### 保存文件
- 点击"保存文件"按钮
- 选择保存位置和文件名
- 选择文件格式（Maya ASCII 或 Maya Binary）
- 插件会验证目录权限并保存文件

#### 状态信息
- 界面底部显示详细的操作日志
- 包含时间戳的操作记录
- 错误和警告信息

## 高级功能

### 停靠面板
插件支持Maya的停靠面板功能：
- 可以停靠到Maya界面的任意位置
- 支持标签页模式
- 响应式设计，适应不同窗口大小

### 错误处理
插件包含完善的错误处理机制：
- 文件不存在检查
- 文件格式验证
- 目录权限检查
- 用户友好的错误消息

### 日志记录
- 所有操作都会记录到Python日志系统
- 界面中显示实时状态信息
- 便于调试和问题排查

## 卸载方法

### 自动卸载
```bash
python install_plugin.py uninstall
```

### 手动卸载
1. 删除Maya scripts目录中的 `maya_file_manager.py`
2. 从 `userSetup.py` 中移除相关代码
3. 重启Maya

## 故障排除

### 常见问题

**Q: 插件菜单没有出现**
A: 
- 确认文件已正确复制到scripts目录
- 检查userSetup.py是否包含加载代码
- 重启Maya
- 查看脚本编辑器中的错误信息

**Q: 文件对话框无法打开**
A:
- 确认Maya版本支持PySide2
- 检查文件权限
- 查看错误日志

**Q: 保存文件失败**
A:
- 检查目标目录的写入权限
- 确认文件名有效
- 检查磁盘空间

### 调试模式

如果遇到问题，可以在脚本编辑器中运行以下代码启用详细日志：

```python
import logging
logging.getLogger('maya_file_manager').setLevel(logging.DEBUG)

import maya_file_manager
maya_file_manager.show_file_manager()
```

## 开发信息

### 文件结构
```
maya_file_manager/
├── maya_file_manager.py    # 主插件文件
├── install_plugin.py       # 安装脚本
├── userSetup.py            # Maya启动脚本
└── README.md               # 说明文档
```

### 技术栈
- **UI框架**: PySide2
- **Maya API**: maya.cmds, maya.mel
- **停靠支持**: MayaQWidgetDockableMixin
- **日志**: Python logging模块

### 自定义扩展

您可以通过以下方式扩展插件功能：

1. **添加文件格式支持**:
   ```python
   # 在 supported_formats 字典中添加新格式
   self.supported_formats['OBJ Files (*.obj)'] = '*.obj'
   ```

2. **自定义回调函数**:
   ```python
   # 连接到文件操作信号
   file_manager.file_opened.connect(your_callback_function)
   file_manager.file_saved.connect(your_callback_function)
   ```

## 版本历史

### v1.0 (当前版本)
- 初始发布
- 基本文件打开/保存功能
- PySide2界面
- Maya集成
- 错误处理和日志记录

## 许可证

此插件为开源项目，遵循MIT许可证。

## 支持

如果您遇到问题或有功能建议，请：
1. 检查故障排除部分
2. 查看Maya脚本编辑器中的错误信息
3. 启用调试模式获取详细日志

## 贡献

欢迎提交问题报告和功能请求！
