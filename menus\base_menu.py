# -*- coding: utf-8 -*-
"""菜单基类 | 作者: MMQ"""

from abc import ABC, abstractmethod

class MenuBase(ABC):
    """菜单基类"""
    
    def __init__(self):
        self.menu_name = "FileManager"
        self.menu_label = "文件管理器"
        self.available = False
    
    @abstractmethod
    def create_menu(self, controller=None):
        """创建菜单"""
        pass
    
    @abstractmethod
    def remove_menu(self):
        """移除菜单"""
        pass
    
    def is_available(self):
        """检查菜单是否可用"""
        return self.available
    
    def update_menu_state(self, controller):
        """更新菜单状态"""
        pass
    
    def add_custom_menu_item(self, label, command, icon=None):
        """添加自定义菜单项"""
        return False
