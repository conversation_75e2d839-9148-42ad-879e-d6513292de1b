# -*- coding: utf-8 -*-
"""插件基类 | 作者: MMQ"""

from abc import ABC, abstractmethod

class PluginBase(ABC):
    """插件基类"""
    
    @abstractmethod
    def get_name(self):
        """返回插件名称"""
        pass
    
    @abstractmethod
    def get_file_extension(self):
        """返回文件扩展名"""
        pass
    
    @abstractmethod
    def open_file(self, file_path):
        """打开文件"""
        pass
    
    @abstractmethod
    def save_file(self, file_path):
        """保存文件"""
        pass
    
    @abstractmethod
    def check_unsaved_changes(self):
        """检查未保存的更改"""
        pass
    
    @abstractmethod
    def get_current_file_info(self):
        """获取当前文件信息"""
        pass
    
    def is_available(self):
        """检查插件是否可用"""
        return True
    
    def get_supported_formats(self):
        """获取支持的文件格式"""
        return [self.get_file_extension()]
    
    def validate_file(self, file_path):
        """验证文件是否有效"""
        import os
        if not os.path.exists(file_path):
            return False
        
        ext = os.path.splitext(file_path)[1].lower()
        return ext in self.get_supported_formats()
