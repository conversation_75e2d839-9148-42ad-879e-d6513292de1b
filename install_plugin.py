# -*- coding: utf-8 -*-
"""
Maya文件管理器插件安装脚本
作者: MMQ
版本: 1.0

此脚本用于安装Maya文件管理器插件到Maya的scripts目录
"""

import os
import sys
import shutil
import subprocess

def get_maya_scripts_path():
    """获取Maya scripts目录路径"""
    try:
        # 尝试导入Maya模块来获取路径
        import maya.cmds as cmds
        maya_app_dir = cmds.internalVar(userAppDir=True)
        scripts_dir = os.path.join(maya_app_dir, "scripts")
        return scripts_dir
    except ImportError:
        # 如果不在Maya环境中，使用默认路径
        import platform
        system = platform.system()
        
        if system == "Windows":
            # Windows路径
            maya_versions = ["2024", "2023", "2022", "2021", "2020"]
            documents_path = os.path.expanduser("~/Documents")
            
            for version in maya_versions:
                maya_path = os.path.join(documents_path, f"maya/{version}/scripts")
                if os.path.exists(maya_path):
                    return maya_path
            
            # 如果没找到，返回最新版本的路径
            return os.path.join(documents_path, "maya/2024/scripts")
            
        elif system == "Darwin":  # macOS
            maya_versions = ["2024", "2023", "2022", "2021", "2020"]
            home_path = os.path.expanduser("~")
            
            for version in maya_versions:
                maya_path = os.path.join(home_path, f"Library/Preferences/Autodesk/maya/{version}/scripts")
                if os.path.exists(maya_path):
                    return maya_path
            
            return os.path.join(home_path, "Library/Preferences/Autodesk/maya/2024/scripts")
            
        else:  # Linux
            maya_versions = ["2024", "2023", "2022", "2021", "2020"]
            home_path = os.path.expanduser("~")
            
            for version in maya_versions:
                maya_path = os.path.join(home_path, f"maya/{version}/scripts")
                if os.path.exists(maya_path):
                    return maya_path
            
            return os.path.join(home_path, "maya/2024/scripts")

def install_plugin():
    """安装插件"""
    try:
        print("=" * 50)
        print("Maya文件管理器插件安装程序")
        print("=" * 50)
        
        # 获取当前脚本目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        plugin_file = os.path.join(current_dir, "maya_file_manager.py")
        
        # 检查插件文件是否存在
        if not os.path.exists(plugin_file):
            print(f"错误: 找不到插件文件 {plugin_file}")
            return False
        
        # 获取Maya scripts目录
        scripts_dir = get_maya_scripts_path()
        print(f"Maya scripts目录: {scripts_dir}")
        
        # 创建scripts目录（如果不存在）
        if not os.path.exists(scripts_dir):
            os.makedirs(scripts_dir)
            print(f"已创建目录: {scripts_dir}")
        
        # 复制插件文件
        target_file = os.path.join(scripts_dir, "maya_file_manager.py")
        shutil.copy2(plugin_file, target_file)
        print(f"已复制插件文件到: {target_file}")
        
        # 创建或更新userSetup.py
        setup_usersetup(scripts_dir)
        
        print("\n" + "=" * 50)
        print("安装完成!")
        print("=" * 50)
        print("请重启Maya以加载插件。")
        print("插件加载后，您可以在Maya菜单栏中找到'文件管理器'菜单。")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"安装过程中发生错误: {str(e)}")
        return False

def setup_usersetup(scripts_dir):
    """设置或更新userSetup.py文件"""
    usersetup_file = os.path.join(scripts_dir, "userSetup.py")
    
    # 要添加的代码
    plugin_code = """
# Maya文件管理器插件自动加载
try:
    import maya_file_manager
    maya_file_manager.initialize_plugin()
    print("Maya文件管理器插件已自动加载")
except Exception as e:
    print(f"加载Maya文件管理器插件时发生错误: {str(e)}")
"""
    
    # 检查userSetup.py是否存在
    if os.path.exists(usersetup_file):
        # 读取现有内容
        with open(usersetup_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经包含我们的代码
        if "maya_file_manager" in content:
            print("userSetup.py已包含文件管理器插件的加载代码")
            return
        
        # 添加我们的代码
        with open(usersetup_file, 'a', encoding='utf-8') as f:
            f.write(plugin_code)
        print("已更新userSetup.py文件")
    else:
        # 创建新的userSetup.py文件
        with open(usersetup_file, 'w', encoding='utf-8') as f:
            f.write(plugin_code)
        print("已创建userSetup.py文件")

def uninstall_plugin():
    """卸载插件"""
    try:
        print("=" * 50)
        print("Maya文件管理器插件卸载程序")
        print("=" * 50)
        
        # 获取Maya scripts目录
        scripts_dir = get_maya_scripts_path()
        
        # 删除插件文件
        plugin_file = os.path.join(scripts_dir, "maya_file_manager.py")
        if os.path.exists(plugin_file):
            os.remove(plugin_file)
            print(f"已删除插件文件: {plugin_file}")
        
        # 删除编译的Python文件
        pyc_file = os.path.join(scripts_dir, "maya_file_manager.pyc")
        if os.path.exists(pyc_file):
            os.remove(pyc_file)
            print(f"已删除编译文件: {pyc_file}")
        
        # 更新userSetup.py
        cleanup_usersetup(scripts_dir)
        
        print("\n" + "=" * 50)
        print("卸载完成!")
        print("=" * 50)
        print("请重启Maya以完全移除插件。")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"卸载过程中发生错误: {str(e)}")
        return False

def cleanup_usersetup(scripts_dir):
    """清理userSetup.py中的插件代码"""
    usersetup_file = os.path.join(scripts_dir, "userSetup.py")
    
    if not os.path.exists(usersetup_file):
        return
    
    try:
        # 读取文件内容
        with open(usersetup_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 过滤掉包含maya_file_manager的行
        filtered_lines = []
        skip_block = False
        
        for line in lines:
            if "Maya文件管理器插件自动加载" in line:
                skip_block = True
                continue
            elif skip_block and line.strip() == "":
                skip_block = False
                continue
            elif skip_block and ("maya_file_manager" in line or line.startswith("    ") or line.startswith("except")):
                continue
            else:
                skip_block = False
                filtered_lines.append(line)
        
        # 写回文件
        with open(usersetup_file, 'w', encoding='utf-8') as f:
            f.writelines(filtered_lines)
        
        print("已清理userSetup.py文件")
        
    except Exception as e:
        print(f"清理userSetup.py时发生错误: {str(e)}")

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "uninstall":
        uninstall_plugin()
    else:
        install_plugin()

if __name__ == "__main__":
    main()
