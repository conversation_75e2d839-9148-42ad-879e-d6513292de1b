# -*- coding: utf-8 -*-
"""通用文件管理器 - 支持插件化的版本控制工具 | 作者: MMQ"""

import os
import datetime
import subprocess
import platform
from abc import ABC, abstractmethod

try:
    from PySide2.QtWidgets import *
    from PySide2.QtCore import *
    from PySide2.QtGui import *
    print("✅ PySide2模块导入成功")
except ImportError:
    try:
        from PyQt5.QtWidgets import *
        from PyQt5.QtCore import *
        from PyQt5.QtGui import *
        print("✅ PyQt5模块导入成功")
    except ImportError:
        print("❌ 需要安装PySide2或PyQt5")
        raise

class FileManagerPlugin(ABC):
    """文件管理器插件基类"""
    
    @abstractmethod
    def get_name(self):
        """返回插件名称"""
        pass
    
    @abstractmethod
    def get_file_extension(self):
        """返回文件扩展名（如.ma, .blend, .max等）"""
        pass
    
    @abstractmethod
    def open_file(self, file_path):
        """打开文件的具体实现"""
        pass
    
    @abstractmethod
    def save_file(self, file_path):
        """保存文件的具体实现"""
        pass
    
    @abstractmethod
    def check_unsaved_changes(self):
        """检查是否有未保存的更改"""
        pass
    
    @abstractmethod
    def get_current_file_info(self):
        """获取当前文件信息"""
        pass

class UniversalFileManager(QWidget):
    """通用文件管理器主类"""
    
    def __init__(self, plugin=None, save_dir=None, base_name=None, parent=None):
        super().__init__(parent)
        
        # 插件和配置
        self.plugin = plugin
        self.save_dir = save_dir or os.path.expanduser("~/Documents/Projects")
        self.base_name = base_name or "project"
        self.log_expanded = False
        
        # 窗口设置
        self.setWindowTitle(f"文件管理器 - {plugin.get_name() if plugin else '通用'}")
        self.setMinimumSize(450, 400)
        self.setMaximumSize(800, 800)
        self.resize(500, 500)
        
        # 创建界面
        self.create_ui()
        self.update_file_list()
        self.log(f"启动成功！插件: {plugin.get_name() if plugin else '无'}")
    
    def set_plugin(self, plugin):
        """设置或更换插件"""
        self.plugin = plugin
        self.setWindowTitle(f"文件管理器 - {plugin.get_name()}")
        self.update_file_list()
        self.log(f"已切换到插件: {plugin.get_name()}")
    
    def create_ui(self):
        """创建用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(6)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 通用样式
        self.setStyleSheet("""
            QWidget{background-color:#393939;color:#CCCCCC;font-family:"Segoe UI";font-size:11px;}
            QPushButton{background-color:#4A4A4A;border:1px solid #5A5A5A;color:#CCCCCC;padding:8px 16px;border-radius:3px;font-weight:bold;}
            QPushButton:hover{background-color:#5A5A5A;}QPushButton:pressed{background-color:#3A3A3A;}
            QComboBox{background-color:#4A4A4A;border:2px solid #5A5A5A;color:#FFFFFF;padding:8px 12px;border-radius:6px;font-size:14px;font-weight:bold;}
            QComboBox:hover{border-color:#6A6A6A;}
            QComboBox::drop-down{border:none;width:20px;}
            QComboBox::down-arrow{image:none;width:0;height:0;border-left:6px solid transparent;border-right:6px solid transparent;border-top:6px solid #FFFFFF;margin-right:6px;}
            QComboBox QAbstractItemView{background-color:#4A4A4A;border:2px solid #5A5A5A;selection-background-color:#6A6A6A;outline:none;font-size:13px;padding:4px;}
            QComboBox QAbstractItemView::item{padding:6px 8px;border-bottom:1px solid #5A5A5A;}
            QTextEdit{background-color:#2A2A2A;border:1px solid #5A5A5A;color:#CCCCCC;padding:4px;border-radius:3px;}
            QLabel{color:#CCCCCC;background-color:transparent;}
        """)
        
        # 标题
        title = QLabel(f"文件管理器 - {self.plugin.get_name() if self.plugin else '通用'}")
        title.setStyleSheet("font-size:14px;font-weight:bold;color:#FFFFFF;padding:8px 0;border-bottom:1px solid #5A5A5A;margin-bottom:8px;")
        layout.addWidget(title)
        
        # 配置区域
        config_layout = QHBoxLayout()
        config_layout.addWidget(QLabel("保存目录:"))
        self.dir_edit = QLineEdit(self.save_dir)
        self.dir_edit.textChanged.connect(self.on_dir_changed)
        config_layout.addWidget(self.dir_edit)
        
        self.browse_btn = QPushButton("浏览")
        self.browse_btn.clicked.connect(self.browse_directory)
        config_layout.addWidget(self.browse_btn)
        layout.addLayout(config_layout)
        
        # 项目名称
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("项目名称:"))
        self.name_edit = QLineEdit(self.base_name)
        self.name_edit.textChanged.connect(self.on_name_changed)
        name_layout.addWidget(self.name_edit)
        layout.addLayout(name_layout)
        
        # 版本选择器
        list_label = QLabel("文件版本选择器:")
        list_label.setStyleSheet("font-weight:bold;margin-bottom:4px;margin-top:8px;")
        layout.addWidget(list_label)
        
        self.version_selector = QComboBox()
        self.version_selector.setFixedHeight(40)
        self.version_selector.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.version_selector.currentTextChanged.connect(self.on_version_selected)
        layout.addWidget(self.version_selector)
        
        # 当前选择信息
        self.selection_info = QTextEdit()
        self.selection_info.setMinimumHeight(80)
        self.selection_info.setMaximumHeight(200)
        self.selection_info.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.selection_info.setStyleSheet("background-color:#4A4A4A;border:1px solid #5A5A5A;border-radius:3px;padding:8px;font-size:11px;color:#CCCCCC;")
        self.selection_info.setReadOnly(True)
        self.selection_info.mousePressEvent = self.open_file_location
        self.selection_info.setCursor(Qt.PointingHandCursor)
        layout.addWidget(self.selection_info)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        self.open_btn = QPushButton("打开文件")
        self.open_btn.setFixedHeight(40)
        self.open_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.open_btn.clicked.connect(self.open_selected_file)
        button_layout.addWidget(self.open_btn)
        
        self.save_btn = QPushButton("保存文件")
        self.save_btn.setFixedHeight(40)
        self.save_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.save_btn.clicked.connect(self.save_to_selected)
        button_layout.addWidget(self.save_btn)
        
        layout.addLayout(button_layout)
        
        # 日志区域
        layout.addSpacing(10)
        log_header = QHBoxLayout()
        self.log_toggle_btn = QPushButton("▶ 操作日志")
        self.log_toggle_btn.setFixedHeight(25)
        self.log_toggle_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.log_toggle_btn.setStyleSheet("text-align:left;padding:4px;font-weight:bold;border:none;background-color:transparent;")
        self.log_toggle_btn.clicked.connect(self.toggle_log)
        log_header.addWidget(self.log_toggle_btn)
        log_header.addStretch()
        layout.addLayout(log_header)
        
        self.log_text = QTextEdit()
        self.log_text.setMinimumHeight(60)
        self.log_text.setMaximumHeight(150)
        self.log_text.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.log_text.setStyleSheet("background-color:#2A2A2A;border:1px solid #5A5A5A;color:#CCCCCC;padding:4px;border-radius:3px;font-family:monospace;font-size:10px;")
        self.log_text.setReadOnly(True)
        self.log_text.hide()
        layout.addWidget(self.log_text)
        
        layout.addStretch()
    
    def on_dir_changed(self, text):
        """目录改变回调"""
        self.save_dir = text
        self.update_file_list()
    
    def on_name_changed(self, text):
        """项目名称改变回调"""
        self.base_name = text
        self.update_file_list()
    
    def browse_directory(self):
        """浏览选择目录"""
        directory = QFileDialog.getExistingDirectory(self, "选择保存目录", self.save_dir)
        if directory:
            self.dir_edit.setText(directory)
    
    def toggle_log(self):
        """切换日志显示"""
        self.log_expanded = not self.log_expanded
        if self.log_expanded:
            self.log_text.show()
            self.log_toggle_btn.setText("▼ 操作日志")
        else:
            self.log_text.hide()
            self.log_toggle_btn.setText("▶ 操作日志")
    
    def log(self, msg):
        """记录日志"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {msg}")
        print(f"[文件管理器] {msg}")
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())
    
    def get_file_extension(self):
        """获取文件扩展名"""
        return self.plugin.get_file_extension() if self.plugin else ".txt"
    
    def get_existing_versions(self):
        """获取已存在的版本号"""
        versions = []
        if os.path.exists(self.save_dir):
            try:
                ext = self.get_file_extension()
                for f in os.listdir(self.save_dir):
                    if f.startswith(self.base_name) and f.endswith(ext):
                        parts = f.replace(ext, '').split('_')
                        if len(parts) >= 2 and parts[-1].startswith('v') and parts[-1][1:].isdigit():
                            versions.append(parts[-1])
            except: pass
        return sorted(list(set(versions)), reverse=True)
    
    def generate_version_list(self):
        """生成版本列表"""
        existing_versions = self.get_existing_versions()
        max_version_num = 0
        for ver in existing_versions:
            try:
                num = int(ver[1:])
                max_version_num = max(max_version_num, num)
            except: pass
        
        all_versions = list(existing_versions)
        start_version = max(max_version_num + 1, 1)
        for i in range(start_version, start_version + 5):
            version = f"v{i:03d}"
            if version not in existing_versions:
                all_versions.append(version)
        
        all_versions.sort(key=lambda x: int(x[1:]), reverse=True)
        return all_versions
    
    def update_file_list(self):
        """更新版本选择器"""
        try:
            self.version_selector.clear()
            versions = self.generate_version_list()
            existing_versions = self.get_existing_versions()
            latest_existing_version = existing_versions[0] if existing_versions else None
            
            ext = self.get_file_extension()
            for i, ver in enumerate(versions):
                filename = f"{self.base_name}_{ver}{ext}"
                file_path = os.path.join(self.save_dir, filename)
                exists = os.path.exists(file_path)
                
                display_text = f"{ver} - {filename}"
                self.version_selector.addItem(display_text, ver)
                
                # 设置颜色
                try:
                    item = self.version_selector.model().item(i)
                    if exists:
                        if ver == latest_existing_version:
                            item.setForeground(QColor(46, 204, 113))  # 绿色
                        else:
                            item.setForeground(QColor(231, 76, 60))   # 红色
                    else:
                        item.setForeground(QColor(255, 255, 255))     # 白色
                except: pass
            
            # 默认选择
            default_index = 0
            if latest_existing_version:
                for i in range(self.version_selector.count()):
                    if self.version_selector.itemData(i) == latest_existing_version:
                        default_index = i
                        break
                self.log(f"默认选择最新版本: {latest_existing_version}")
            else:
                for i in range(self.version_selector.count()):
                    if self.version_selector.itemData(i) == "v001":
                        default_index = i
                        break
                self.log("路径下无文件，默认选择第一版: v001")
            
            if self.version_selector.count() > 0:
                self.version_selector.setCurrentIndex(default_index)
            
            self.log(f"版本选择器已更新，共 {len(versions)} 个版本可选")
            
        except Exception as e:
            self.log(f"更新版本选择器错误: {e}")

    def on_version_selected(self, display_text):
        """版本选择改变时的回调"""
        if not display_text:
            return

        try:
            current_index = self.version_selector.currentIndex()
            version = self.version_selector.itemData(current_index)

            if version:
                ext = self.get_file_extension()
                filename = f"{self.base_name}_{version}{ext}"
                file_path = os.path.join(self.save_dir, filename)
                exists = os.path.exists(file_path)

                existing_versions = self.get_existing_versions()
                latest_existing_version = existing_versions[0] if existing_versions else None

                # 设置状态颜色
                if exists:
                    if version == latest_existing_version:
                        status_color = "🟢"
                    else:
                        status_color = "🔴"
                else:
                    status_color = "⚪"

                status_text = f"当前选择: {status_color} {version}\n文件名: {filename}"

                if exists:
                    try:
                        size = os.path.getsize(file_path) / 1024 / 1024
                        mod_time = datetime.datetime.fromtimestamp(os.path.getmtime(file_path)).strftime("%Y-%m-%d %H:%M")
                        status_text += f"\n状态: 文件已存在\n大小: {size:.2f} MB\n修改时间: {mod_time}"
                    except:
                        status_text += "\n状态: 文件已存在"
                else:
                    status_text += "\n状态: 新版本 (文件不存在)"

                status_text += f"\n\n📁 文件路径:\n{file_path}\n(点击此区域打开文件夹)"
                self.selection_info.setPlainText(status_text)
                self.current_file_path = file_path
                self.log(f"选择版本: {version}")

        except Exception as e:
            self.log(f"版本选择回调错误: {e}")

    def open_file_location(self, event):
        """打开文件所在位置"""
        try:
            if hasattr(self, 'current_file_path') and self.current_file_path:
                directory = os.path.dirname(self.current_file_path)

                if not os.path.exists(directory):
                    try:
                        os.makedirs(directory)
                        self.log(f"已创建目录: {directory}")
                    except Exception as e:
                        self.log(f"无法创建目录: {str(e)}")
                        return

                system = platform.system()
                if system == "Windows":
                    if os.path.exists(self.current_file_path):
                        subprocess.run(['explorer', '/select,', os.path.normpath(self.current_file_path)])
                    else:
                        subprocess.run(['explorer', os.path.normpath(directory)])
                elif system == "Darwin":
                    if os.path.exists(self.current_file_path):
                        subprocess.run(['open', '-R', self.current_file_path])
                    else:
                        subprocess.run(['open', directory])
                else:
                    subprocess.run(['xdg-open', directory])

                self.log(f"已打开文件位置: {directory}")
            else:
                if os.path.exists(self.save_dir):
                    system = platform.system()
                    if system == "Windows":
                        subprocess.run(['explorer', os.path.normpath(self.save_dir)])
                    elif system == "Darwin":
                        subprocess.run(['open', self.save_dir])
                    else:
                        subprocess.run(['xdg-open', self.save_dir])
                    self.log(f"已打开默认保存目录: {self.save_dir}")
                else:
                    self.log("保存目录不存在")
        except Exception as e:
            self.log(f"打开文件位置失败: {str(e)}")

    def confirm_overwrite(self, path, ver):
        """确认覆盖文件"""
        if os.path.exists(path):
            reply = QMessageBox.question(self, "文件已存在",
                                       f"版本 {ver} 已存在，是否覆盖？\n\n{os.path.basename(path)}",
                                       QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.No:
                self.log("用户取消覆盖")
                return False
        return True

    def open_selected_file(self):
        """打开选中文件"""
        if not self.plugin:
            QMessageBox.warning(self, "无插件", "请先设置文件操作插件")
            return

        try:
            current_index = self.version_selector.currentIndex()
            if current_index < 0:
                QMessageBox.warning(self, "未选择文件", "请先选择要打开的文件版本")
                return

            version = self.version_selector.itemData(current_index)
            ext = self.get_file_extension()
            filename = f"{self.base_name}_{version}{ext}"
            file_path = os.path.join(self.save_dir, filename)

            if not os.path.exists(file_path):
                QMessageBox.warning(self, "文件不存在", f"版本 {version} 的文件不存在:\n{filename}")
                return

            # 检查未保存更改
            if self.plugin.check_unsaved_changes():
                reply = QMessageBox.question(self, "确认", "当前有未保存的更改，是否继续打开？",
                                           QMessageBox.Yes | QMessageBox.No)
                if reply == QMessageBox.No:
                    self.log("用户取消打开操作")
                    return

            # 使用插件打开文件
            success = self.plugin.open_file(file_path)
            if success:
                self.log(f"成功打开: {filename}")
                QMessageBox.information(self, "打开成功", f"已打开版本 {version}:\n{filename}")
            else:
                self.log(f"打开失败: {filename}")
                QMessageBox.critical(self, "打开失败", f"无法打开文件: {filename}")

        except Exception as e:
            error_msg = f"打开文件失败: {str(e)}"
            self.log(error_msg)
            QMessageBox.critical(self, "打开失败", error_msg)

    def save_to_selected(self):
        """保存到选中版本"""
        if not self.plugin:
            QMessageBox.warning(self, "无插件", "请先设置文件操作插件")
            return

        try:
            current_index = self.version_selector.currentIndex()
            if current_index < 0:
                QMessageBox.warning(self, "未选择版本", "请先选择要保存的版本")
                return

            version = self.version_selector.itemData(current_index)
            ext = self.get_file_extension()
            filename = f"{self.base_name}_{version}{ext}"
            file_path = os.path.join(self.save_dir, filename)

            # 创建目录
            if not os.path.exists(self.save_dir):
                try:
                    os.makedirs(self.save_dir)
                    self.log(f"已创建目录: {self.save_dir}")
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"无法创建保存目录: {str(e)}")
                    return

            # 确认覆盖
            if not self.confirm_overwrite(file_path, version):
                return

            # 使用插件保存文件
            self.log(f"正在保存到版本 {version}: {filename}")
            success = self.plugin.save_file(file_path)
            if success:
                self.log(f"保存成功: {filename}")
                self.update_file_list()
                QMessageBox.information(self, "保存成功", f"文件已保存为版本 {version}:\n{filename}")
            else:
                self.log(f"保存失败: {filename}")
                QMessageBox.critical(self, "保存失败", f"无法保存文件: {filename}")

        except Exception as e:
            error_msg = f"保存文件失败: {str(e)}"
            self.log(error_msg)
            QMessageBox.critical(self, "保存失败", error_msg)

def create_file_manager(plugin=None, save_dir=None, base_name=None, parent=None):
    """创建文件管理器实例的工厂函数"""
    return UniversalFileManager(plugin, save_dir, base_name, parent)
