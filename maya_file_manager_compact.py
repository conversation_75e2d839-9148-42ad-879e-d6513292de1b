# -*- coding: utf-8 -*-
"""Maya文件管理器 - 文件列表版 | 作者: MMQ | Maya 2022.5.1"""

print("🚀 启动Maya文件管理器...")
try:
    import maya.cmds as cmds, os, datetime
    from maya.app.general.mayaMixin import MayaQWidgetDockableMixin
    from PySide2.QtWidgets import *
    from PySide2.QtCore import *
    print("✅ 模块导入成功")
except ImportError as e: print(f"❌ 导入失败: {e}")

class MayaFileManager(MayaQWidgetDockableMixin, QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Maya文件管理器")
        self.setMinimumSize(400, 500)
        self.setObjectName("MayaFileManagerCompact")
        self.save_dir, self.base_name = "D:/dev/Scgtest/0010/3d/mm/", "dev_cgtest_0010_mm"
        self.create_ui()
        self.update_file_list()
        self.log(f"启动成功！Maya: {cmds.about(version=True)}")
        
    def create_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(8)
        layout.setContentsMargins(8, 8, 8, 8)

        # Maya风格样式
        self.setStyleSheet("""
            QWidget{background-color:#393939;color:#CCCCCC;font-family:"Segoe UI";font-size:11px;}
            QPushButton{background-color:#4A4A4A;border:1px solid #5A5A5A;color:#CCCCCC;padding:8px 16px;border-radius:3px;font-weight:bold;}
            QPushButton:hover{background-color:#5A5A5A;}QPushButton:pressed{background-color:#3A3A3A;}
            QListWidget{background-color:#2A2A2A;border:1px solid #5A5A5A;color:#CCCCCC;padding:4px;border-radius:3px;outline:none;}
            QListWidget::item{padding:8px;border-bottom:1px solid #4A4A4A;}
            QListWidget::item:selected{background-color:#5A5A5A;color:#FFFFFF;}
            QListWidget::item:hover{background-color:#4A4A4A;}
            QScrollBar:vertical{background-color:#2A2A2A;width:12px;border:none;}
            QScrollBar::handle:vertical{background-color:#5A5A5A;border-radius:6px;min-height:20px;}
            QScrollBar::handle:vertical:hover{background-color:#6A6A6A;}
            QScrollBar::add-line:vertical,QScrollBar::sub-line:vertical{height:0px;}
            QTextEdit{background-color:#2A2A2A;border:1px solid #5A5A5A;color:#CCCCCC;padding:4px;border-radius:3px;}
            QLabel{color:#CCCCCC;background-color:transparent;}
        """)

        # 标题
        title = QLabel("Maya文件管理器")
        title.setStyleSheet("font-size:14px;font-weight:bold;color:#FFFFFF;padding:8px 0;border-bottom:1px solid #5A5A5A;margin-bottom:8px;")
        layout.addWidget(title)

        # 文件列表标签
        list_label = QLabel("文件版本列表 (最新9个版本):")
        list_label.setStyleSheet("font-weight:bold;margin-bottom:4px;")
        layout.addWidget(list_label)

        # 文件列表
        self.file_list = QListWidget()
        self.file_list.setMinimumHeight(200)
        self.file_list.currentItemChanged.connect(self.on_file_selected)
        layout.addWidget(self.file_list)

        # 当前选择信息
        self.selection_info = QLabel("当前选择: 无")
        self.selection_info.setStyleSheet("background-color:#4A4A4A;border:1px solid #5A5A5A;border-radius:3px;padding:8px;font-size:11px;")
        layout.addWidget(self.selection_info)

        # 按钮区域
        button_layout = QHBoxLayout()

        self.open_btn = QPushButton("Open")
        self.open_btn.setMinimumHeight(35)
        self.open_btn.clicked.connect(self.open_selected_file)
        button_layout.addWidget(self.open_btn)

        self.save_btn = QPushButton("Save")
        self.save_btn.setMinimumHeight(35)
        self.save_btn.clicked.connect(self.save_to_selected)
        button_layout.addWidget(self.save_btn)

        layout.addLayout(button_layout)

        # 日志区域
        log_label = QLabel("操作日志:")
        log_label.setStyleSheet("font-weight:bold;margin-top:8px;margin-bottom:4px;")
        layout.addWidget(log_label)

        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(80)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)

    def log(self, msg):
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {msg}")
        print(f"[文件管理器] {msg}")
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())

    def get_versions(self):
        """获取已存在的版本号"""
        versions = []
        if os.path.exists(self.save_dir):
            try:
                for f in os.listdir(self.save_dir):
                    if f.startswith(self.base_name) and f.endswith(('.ma', '.mb')):
                        parts = f.split('_')
                        if len(parts) >= 5 and parts[4].startswith('v') and parts[4][1:].isdigit():
                            versions.append(parts[4])
            except: pass
        return sorted(list(set(versions)), reverse=True)  # 降序排列，最新版本在前

    def get_next_version(self, versions):
        """获取下一个版本号"""
        if not versions: return "v001"
        max_ver = max([int(v[1:]) for v in versions if v[1:].isdigit()] or [0])
        return f"v{max_ver + 1:03d}"

    def update_file_list(self):
        """更新文件列表"""
        try:
            self.file_list.clear()
            versions = self.get_versions()
            next_ver = self.get_next_version(versions)

            # 显示最新的9个版本 + 下一个新版本
            display_versions = versions[:9] + [next_ver]

            for i, ver in enumerate(display_versions):
                filename = f"{self.base_name}_{ver}_mmq.ma"
                file_path = os.path.join(self.save_dir, filename)

                # 检查文件是否存在
                exists = os.path.exists(file_path)
                status = "✓" if exists else "○"

                # 创建列表项
                item_text = f"{status} {ver} - {filename}"
                if ver == next_ver and not exists:
                    item_text += " (新版本)"

                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, ver)  # 存储版本号
                self.file_list.addItem(item)

                # 默认选择第一个（最新版本）
                if i == 0:
                    self.file_list.setCurrentItem(item)

            self.log(f"文件列表已更新，显示 {len(display_versions)} 个版本")

        except Exception as e:
            self.log(f"更新文件列表错误: {e}")

    def on_file_selected(self, current, previous):
        """文件选择改变时的回调"""
        if current:
            version = current.data(Qt.UserRole)
            filename = f"{self.base_name}_{version}_mmq.ma"
            file_path = os.path.join(self.save_dir, filename)
            exists = os.path.exists(file_path)

            status_text = f"当前选择: {version} - {filename}"
            if exists:
                try:
                    size = os.path.getsize(file_path) / 1024 / 1024  # MB
                    mod_time = datetime.datetime.fromtimestamp(os.path.getmtime(file_path)).strftime("%Y-%m-%d %H:%M")
                    status_text += f"\n文件大小: {size:.2f} MB | 修改时间: {mod_time}"
                except:
                    status_text += "\n文件存在"
            else:
                status_text += "\n文件不存在 (可创建新版本)"

            self.selection_info.setText(status_text)
            self.log(f"选择版本: {version}")

    def filename(self, ver):
        return f"{self.base_name}_{ver}_mmq.ma"

    def confirm_overwrite(self, path, ver):
        """确认覆盖文件"""
        if os.path.exists(path):
            reply = QMessageBox.question(self, "文件已存在",
                                       f"版本 {ver} 已存在，是否覆盖？\n\n{os.path.basename(path)}",
                                       QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.No:
                self.log("用户取消覆盖")
                return False
        return True

    def open_selected_file(self):
        """打开当前选择的文件"""
        try:
            current_item = self.file_list.currentItem()
            if not current_item:
                QMessageBox.warning(self, "未选择文件", "请先选择要打开的文件版本")
                return

            version = current_item.data(Qt.UserRole)
            filename = self.filename(version)
            file_path = os.path.join(self.save_dir, filename)

            if not os.path.exists(file_path):
                QMessageBox.warning(self, "文件不存在", f"版本 {version} 的文件不存在:\n{filename}")
                return

            # 检查当前场景是否有未保存的更改
            if cmds.file(query=True, modified=True):
                reply = QMessageBox.question(self, "确认", "当前场景有未保存的更改，是否继续打开？",
                                           QMessageBox.Yes | QMessageBox.No)
                if reply == QMessageBox.No:
                    self.log("用户取消打开操作")
                    return

            # 打开文件
            cmds.file(file_path, open=True, force=True)
            self.log(f"成功打开: {filename}")
            QMessageBox.information(self, "打开成功", f"已打开版本 {version}:\n{filename}")

        except Exception as e:
            error_msg = f"打开文件失败: {str(e)}"
            self.log(error_msg)
            QMessageBox.critical(self, "打开失败", error_msg)

    def save_to_selected(self):
        """保存到当前选择的版本"""
        try:
            current_item = self.file_list.currentItem()
            if not current_item:
                QMessageBox.warning(self, "未选择版本", "请先选择要保存的版本")
                return

            version = current_item.data(Qt.UserRole)
            filename = self.filename(version)
            file_path = os.path.join(self.save_dir, filename)

            # 创建目录（如果不存在）
            if not os.path.exists(self.save_dir):
                try:
                    os.makedirs(self.save_dir)
                    self.log(f"已创建目录: {self.save_dir}")
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"无法创建保存目录: {str(e)}")
                    return

            # 检查文件是否存在，询问覆盖
            if not self.confirm_overwrite(file_path, version):
                return

            # 保存文件
            self.log(f"正在保存到版本 {version}: {filename}")
            cmds.file(rename=file_path)
            cmds.file(save=True, type="mayaAscii")

            self.log(f"保存成功: {filename}")
            self.update_file_list()  # 刷新文件列表
            QMessageBox.information(self, "保存成功", f"文件已保存为版本 {version}:\n{filename}")

        except Exception as e:
            error_msg = f"保存文件失败: {str(e)}"
            self.log(error_msg)
            QMessageBox.critical(self, "保存失败", error_msg)

# 启动逻辑
file_manager = None

def close_existing():
    """关闭已存在的窗口"""
    try:
        for name in ["MayaFileManagerCompactWorkspaceControl", "MayaFileManagerCompact"]:
            if (cmds.workspaceControl(name, exists=True) if "Workspace" in name else cmds.window(name, exists=True)):
                cmds.deleteUI(name)
                print(f"已删除: {name}")
    except: pass

def show_manager():
    """显示文件管理器"""
    global file_manager
    try:
        close_existing()
        if file_manager:
            try:
                file_manager.close()
                file_manager.deleteLater()
            except: pass
            file_manager = None

        file_manager = MayaFileManager()
        file_manager.show(dockable=True)
        print("✅ Maya文件管理器启动成功！")
        return file_manager
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return None

# 中文函数
重启文件管理器 = lambda: show_manager()
关闭现有窗口 = close_existing

# 立即启动
print("正在启动文件管理器...")
try:
    window = show_manager()
    if window:
        print("🎉 文件管理器界面已显示")
        print("📋 功能说明:")
        print("   • 文件列表显示最新9个版本")
        print("   • 上下滚动选择版本")
        print("   • Open按钮打开选中版本")
        print("   • Save按钮保存到选中版本")
    else:
        print("❌ 启动失败")
except Exception as e:
    print(f"❌ 启动异常: {e}")

print("=" * 50)
print("重启命令: show_manager() 或 重启文件管理器()")
print("=" * 50)
