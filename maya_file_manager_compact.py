# -*- coding: utf-8 -*-
"""Maya文件管理器 - 极简版 | 作者: MMQ | Maya 2022.5.1"""

print("🚀 启动Maya文件管理器...")
try:
    import maya.cmds as cmds, os, datetime
    from maya.app.general.mayaMixin import MayaQWidgetDockableMixin
    from PySide2.QtWidgets import *
    from PySide2.QtCore import *
    print("✅ 模块导入成功")
except ImportError as e: print(f"❌ 导入失败: {e}")

class MayaFileManager(MayaQWidgetDockableMixin, QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Maya文件管理器")
        self.setMinimumSize(450, 350)
        self.setObjectName("MayaFileManagerCompact")
        self.save_dir, self.base_name, self.log_expanded = "D:/dev/Scgtest/0010/3d/mm/", "dev_cgtest_0010_mm", True
        self.create_ui()
        self.update_versions()
        self.update_file_info()
        self.log(f"启动成功！Maya: {cmds.about(version=True)}")
        
    def create_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(6)
        layout.setContentsMargins(6, 6, 6, 6)
        
        # Maya风格样式
        self.setStyleSheet("""
            QWidget{background-color:#393939;color:#CCCCCC;font-family:"Segoe UI";font-size:11px;}
            QPushButton{background-color:#4A4A4A;border:1px solid #5A5A5A;color:#CCCCCC;padding:6px 12px;border-radius:3px;}
            QPushButton:hover{background-color:#5A5A5A;}QPushButton:pressed{background-color:#3A3A3A;}
            QComboBox{background-color:#4A4A4A;border:1px solid #5A5A5A;color:#CCCCCC;padding:4px 8px;border-radius:2px;}
            QComboBox::drop-down{border:none;width:16px;background-color:#4A4A4A;}
            QComboBox::down-arrow{image:none;border-left:4px solid transparent;border-right:4px solid transparent;border-top:4px solid #CCCCCC;}
            QComboBox QAbstractItemView{background-color:#4A4A4A;border:1px solid #5A5A5A;selection-background-color:#5A5A5A;}
            QTextEdit{background-color:#2A2A2A;border:1px solid #5A5A5A;color:#CCCCCC;padding:4px;border-radius:3px;}
            QScrollBar:vertical{background-color:#2A2A2A;width:12px;border:none;}
            QScrollBar::handle:vertical{background-color:#5A5A5A;border-radius:6px;min-height:20px;}
            QScrollBar::handle:vertical:hover{background-color:#6A6A6A;}
            QScrollBar::add-line:vertical,QScrollBar::sub-line:vertical{height:0px;}
            QLabel{color:#CCCCCC;background-color:transparent;}
        """)
        
        # 标题
        title = QLabel("文件管理器")
        title.setStyleSheet("font-weight:bold;color:#FFFFFF;padding:4px 0;border-bottom:1px solid #5A5A5A;margin-bottom:6px;")
        layout.addWidget(title)
        
        # 按钮和版本选择区域
        for btn_text, label_text, attr_name, callback in [
            ("打开Maya文件", "打开版本:", "open_version_combo", self.open_file),
            ("保存文件", "版本号:", "version_combo", self.save_file)
        ]:
            h_layout = QHBoxLayout()
            btn = QPushButton(btn_text)
            btn.setMinimumHeight(26)
            btn.clicked.connect(callback)
            h_layout.addWidget(btn, 2)
            
            v_layout = QVBoxLayout()
            v_layout.addWidget(QLabel(label_text))
            combo = QComboBox()
            combo.setMinimumHeight(22)
            setattr(self, attr_name, combo)
            v_layout.addWidget(combo)
            h_layout.addLayout(v_layout, 1)
            layout.addLayout(h_layout)
        
        self.version_combo.currentTextChanged.connect(lambda v: self.log(f"切换到版本: {v}") if v else None)
        
        # 文件信息
        self.file_info = QLabel("当前文件: 新建场景")
        self.file_info.setStyleSheet("background-color:#f8f9fa;border:1px solid #dee2e6;border-radius:4px;padding:8px;font-size:11px;")
        self.file_info.setWordWrap(True)
        layout.addWidget(self.file_info)
        
        # 日志区域
        log_header = QHBoxLayout()
        self.log_toggle_btn = QPushButton("▼ 操作日志")
        self.log_toggle_btn.setStyleSheet("text-align:left;padding:4px;font-weight:bold;border:none;background-color:transparent;")
        self.log_toggle_btn.clicked.connect(self.toggle_log)
        log_header.addWidget(self.log_toggle_btn)
        log_header.addStretch()
        layout.addLayout(log_header)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(100)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
    def toggle_log(self):
        self.log_expanded = not self.log_expanded
        self.log_text.setVisible(self.log_expanded)
        self.log_toggle_btn.setText("▼ 操作日志" if self.log_expanded else "▶ 操作日志")
        
    def log(self, msg):
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {msg}")
        print(f"[文件管理器] {msg}")
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())
        
    def update_file_info(self):
        try:
            scene = cmds.file(query=True, sceneName=True)
            self.file_info.setText(f"当前文件: {os.path.basename(scene) if scene else '新建场景'}\n路径: {scene if scene else '未保存'}")
        except: self.file_info.setText("获取文件信息失败")
            
    def get_versions(self):
        versions = []
        if os.path.exists(self.save_dir):
            try:
                for f in os.listdir(self.save_dir):
                    if f.startswith(self.base_name) and f.endswith(('.ma', '.mb')):
                        parts = f.split('_')
                        if len(parts) >= 5 and parts[4].startswith('v') and parts[4][1:].isdigit():
                            versions.append(parts[4])
            except: pass
        return sorted(list(set(versions)))
    
    def get_next_version(self, versions):
        if not versions: return "v001"
        max_ver = max([int(v[1:]) for v in versions if v[1:].isdigit()] or [0])
        return f"v{max_ver + 1:03d}"
    
    def update_versions(self):
        try:
            versions = self.get_versions()
            next_ver = self.get_next_version(versions)
            
            # 更新保存版本
            self.version_combo.clear()
            for v in versions + [next_ver]: self.version_combo.addItem(v)
            if self.version_combo.count() > 0: self.version_combo.setCurrentIndex(self.version_combo.count() - 1)
            
            # 更新打开版本
            self.open_version_combo.clear()
            if versions:
                for v in versions: self.open_version_combo.addItem(v)
                self.open_version_combo.setCurrentIndex(self.open_version_combo.count() - 1)
            else: self.open_version_combo.addItem("v001")
                
            self.log(f"版本更新完成 - 保存:{self.version_combo.currentText()} 打开:{self.open_version_combo.currentText()}")
        except Exception as e: self.log(f"版本更新错误: {e}")
    
    def filename(self, ver=None): return f"{self.base_name}_{ver or self.version_combo.currentText()}_mmq.ma"
    
    def extract_version(self, filename):
        try:
            for part in filename.split('_'):
                if part.startswith('v') and part[1:].isdigit(): return part
        except: pass
        return None
    
    def confirm_overwrite(self, path, ver):
        if os.path.exists(path):
            reply = QMessageBox.question(self, "文件已存在", f"版本 {ver} 已存在，是否覆盖？\n\n{os.path.basename(path)}", 
                                       QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.No:
                self.log("用户取消覆盖")
                return False
        return True
    
    def open_file(self):
        try:
            ver = self.open_version_combo.currentText()
            path = os.path.join(self.save_dir, self.filename(ver))
            
            if not os.path.exists(path):
                QMessageBox.warning(self, "文件不存在", f"版本 {ver} 不存在:\n{self.filename(ver)}")
                return
            
            if cmds.file(query=True, modified=True):
                if QMessageBox.question(self, "确认", "当前场景有未保存更改，是否继续？") == QMessageBox.No:
                    self.log("取消打开")
                    return
            
            cmds.file(path, open=True, force=True)
            self.log(f"打开成功: {self.filename(ver)}")
            self.update_file_info()
            QMessageBox.information(self, "成功", f"已打开版本 {ver}")
        except Exception as e:
            self.log(f"打开失败: {e}")
            QMessageBox.critical(self, "错误", f"打开失败: {e}")
    
    def save_file(self):
        try:
            scene = cmds.file(query=True, sceneName=True)
            ver = self.version_combo.currentText()
            
            # 检查是否为相同版本的项目文件
            if (scene and self.base_name in os.path.basename(scene) and 
                self.extract_version(os.path.basename(scene)) == ver):
                if not self.confirm_overwrite(scene, ver): return
                cmds.file(save=True)
                self.log(f"保存成功: {os.path.basename(scene)}")
                self.update_file_info()
                QMessageBox.information(self, "成功", "文件保存成功！")
                return
            
            # 另存为新版本
            self.save_as_version(ver)
        except Exception as e:
            self.log(f"保存失败: {e}")
            QMessageBox.critical(self, "错误", f"保存失败: {e}")
    
    def save_as_version(self, ver):
        try:
            if not os.path.exists(self.save_dir):
                try:
                    os.makedirs(self.save_dir)
                    self.log(f"创建目录: {self.save_dir}")
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"无法创建目录: {e}")
                    return
            
            path = os.path.join(self.save_dir, self.filename(ver))
            if not self.confirm_overwrite(path, ver): return
            
            self.log(f"保存中: {self.filename(ver)}")
            cmds.file(rename=path)
            cmds.file(save=True, type="mayaAscii")
            self.log(f"保存成功: {self.filename(ver)}")
            self.update_file_info()
            self.update_versions()
            QMessageBox.information(self, "成功", f"已保存为版本 {ver}")
        except Exception as e:
            self.log(f"保存失败: {e}")
            QMessageBox.critical(self, "错误", f"保存失败: {e}")

# 启动逻辑
file_manager = None
def close_existing():
    try:
        for name in ["MayaFileManagerCompactWorkspaceControl", "MayaFileManagerCompact"]:
            if (cmds.workspaceControl(name, exists=True) if "Workspace" in name else cmds.window(name, exists=True)):
                cmds.deleteUI(name)
    except: pass

def show_manager():
    global file_manager
    try:
        close_existing()
        if file_manager: 
            try: file_manager.close(); file_manager.deleteLater()
            except: pass
        file_manager = MayaFileManager()
        file_manager.show(dockable=True)
        print("✅ 启动成功！")
        return file_manager
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return None

# 中文函数和启动
重启 = lambda: show_manager()
关闭 = close_existing

print("启动中...")
try:
    if show_manager(): print("🎉 文件管理器已显示")
    else: print("❌ 启动失败")
except Exception as e: print(f"❌ 异常: {e}")
print("重启命令: show_manager() 或 重启()")
