# -*- coding: utf-8 -*-
"""Maya文件管理器 - 文件列表版 | 作者: MMQ | Maya 2022.5.1"""

print("🚀 启动Maya文件管理器...")
try:
    import maya.cmds as cmds, os, datetime
    from maya.app.general.mayaMixin import MayaQWidgetDockableMixin
    from PySide2.QtWidgets import *
    from PySide2.QtCore import *
    from PySide2.QtGui import *
    print("✅ 模块导入成功")
except ImportError as e: print(f"❌ 导入失败: {e}")

class MayaFileManager(MayaQWidgetDockableMixin, QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Maya文件管理器")
        self.setMinimumSize(450, 400)
        self.setMaximumSize(600, 600)
        self.resize(500, 450)
        self.setObjectName("MayaFileManagerCompact")
        self.save_dir, self.base_name = "D:/dev/Scgtest/0010/3d/mm/", "dev_cgtest_0010_mm"
        self.log_expanded = False  # 默认折叠日志
        self.create_ui()
        self.update_file_list()
        self.log(f"启动成功！Maya: {cmds.about(version=True)}")
        
    def create_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(6)
        layout.setContentsMargins(10, 10, 10, 10)

        # Maya风格样式
        self.setStyleSheet("""
            QWidget{background-color:#393939;color:#CCCCCC;font-family:"Segoe UI";font-size:11px;}
            QPushButton{background-color:#4A4A4A;border:1px solid #5A5A5A;color:#CCCCCC;padding:8px 16px;border-radius:3px;font-weight:bold;}
            QPushButton:hover{background-color:#5A5A5A;}QPushButton:pressed{background-color:#3A3A3A;}
            QListWidget{background-color:#2A2A2A;border:1px solid #5A5A5A;color:#CCCCCC;padding:4px;border-radius:3px;outline:none;}
            QListWidget::item{padding:8px;border-bottom:1px solid #4A4A4A;}
            QListWidget::item:selected{background-color:#5A5A5A;color:#FFFFFF;}
            QListWidget::item:hover{background-color:#4A4A4A;}
            QScrollBar:vertical{background-color:#2A2A2A;width:12px;border:none;}
            QScrollBar::handle:vertical{background-color:#5A5A5A;border-radius:6px;min-height:20px;}
            QScrollBar::handle:vertical:hover{background-color:#6A6A6A;}
            QScrollBar::add-line:vertical,QScrollBar::sub-line:vertical{height:0px;}
            QTextEdit{background-color:#2A2A2A;border:1px solid #5A5A5A;color:#CCCCCC;padding:4px;border-radius:3px;}
            QLabel{color:#CCCCCC;background-color:transparent;}
        """)

        # 标题
        title = QLabel("Maya文件管理器")
        title.setStyleSheet("font-size:14px;font-weight:bold;color:#FFFFFF;padding:8px 0;border-bottom:1px solid #5A5A5A;margin-bottom:8px;")
        layout.addWidget(title)

        # 文件版本选择器标签
        list_label = QLabel("文件版本选择器:")
        list_label.setStyleSheet("font-weight:bold;margin-bottom:4px;")
        layout.addWidget(list_label)

        # 版本选择器 (类似iPhone滚动选择器)
        self.version_selector = QComboBox()
        self.version_selector.setFixedHeight(40)  # 固定高度
        self.version_selector.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.version_selector.setStyleSheet("""
            QComboBox {
                background-color: #4A4A4A; border: 2px solid #5A5A5A;
                color: #FFFFFF; padding: 8px 12px; border-radius: 6px;
                font-size: 14px; font-weight: bold;
            }
            QComboBox:hover { border-color: #6A6A6A; }
            QComboBox::drop-down { border: none; width: 20px; }
            QComboBox::down-arrow {
                image: none; width: 0; height: 0;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-top: 6px solid #FFFFFF;
                margin-right: 6px;
            }
            QComboBox QAbstractItemView {
                background-color: #4A4A4A; border: 2px solid #5A5A5A;
                selection-background-color: #6A6A6A; outline: none;
                font-size: 13px; padding: 4px;
            }
            QComboBox QAbstractItemView::item {
                padding: 6px 8px;
                border-bottom: 1px solid #5A5A5A;
            }
        """)
        self.version_selector.currentTextChanged.connect(self.on_version_selected)
        layout.addWidget(self.version_selector)

        # 当前选择信息（包含路径）
        self.selection_info = QLabel("当前选择: 无")
        self.selection_info.setFixedHeight(120)  # 固定高度
        self.selection_info.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.selection_info.setStyleSheet("background-color:#4A4A4A;border:1px solid #5A5A5A;border-radius:3px;padding:8px;font-size:11px;")
        self.selection_info.setWordWrap(True)
        self.selection_info.setAlignment(Qt.AlignTop)  # 顶部对齐
        self.selection_info.mousePressEvent = self.open_file_location
        self.selection_info.setCursor(Qt.PointingHandCursor)
        layout.addWidget(self.selection_info)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        self.open_btn = QPushButton("Open")
        self.open_btn.setFixedHeight(40)  # 固定高度
        self.open_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.open_btn.clicked.connect(self.open_selected_file)
        button_layout.addWidget(self.open_btn)

        self.save_btn = QPushButton("Save")
        self.save_btn.setFixedHeight(40)  # 固定高度
        self.save_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.save_btn.clicked.connect(self.save_to_selected)
        button_layout.addWidget(self.save_btn)

        layout.addLayout(button_layout)

        # 日志区域 - 可折叠
        log_header = QHBoxLayout()
        self.log_toggle_btn = QPushButton("▶ 操作日志")
        self.log_toggle_btn.setStyleSheet("text-align:left;padding:4px;font-weight:bold;border:none;background-color:transparent;")
        self.log_toggle_btn.clicked.connect(self.toggle_log)
        log_header.addWidget(self.log_toggle_btn)
        log_header.addStretch()
        layout.addLayout(log_header)

        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(80)
        self.log_text.setReadOnly(True)
        self.log_text.hide()  # 默认隐藏
        layout.addWidget(self.log_text)

    def toggle_log(self):
        """切换日志显示"""
        self.log_expanded = not self.log_expanded
        if self.log_expanded:
            self.log_text.show()
            self.log_toggle_btn.setText("▼ 操作日志")
        else:
            self.log_text.hide()
            self.log_toggle_btn.setText("▶ 操作日志")

    def log(self, msg):
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {msg}")
        print(f"[文件管理器] {msg}")
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())

    def get_existing_versions(self):
        """获取已存在的版本号"""
        versions = []
        if os.path.exists(self.save_dir):
            try:
                for f in os.listdir(self.save_dir):
                    if f.startswith(self.base_name) and f.endswith(('.ma', '.mb')):
                        parts = f.split('_')
                        if len(parts) >= 5 and parts[4].startswith('v') and parts[4][1:].isdigit():
                            versions.append(parts[4])
            except: pass
        return sorted(list(set(versions)), reverse=True)  # 降序排列，最新版本在前

    def generate_version_list(self):
        """生成版本列表 - 包含现有版本和预置的未来版本"""
        existing_versions = self.get_existing_versions()

        # 找到最大版本号
        max_version_num = 0
        for ver in existing_versions:
            try:
                num = int(ver[1:])  # 去掉'v'前缀
                max_version_num = max(max_version_num, num)
            except: pass

        # 如果没有现有版本，确保从v001开始
        if max_version_num == 0:
            max_version_num = 0

        # 生成版本列表：现有版本 + 预置5个未来版本
        all_versions = []

        # 添加现有版本
        all_versions.extend(existing_versions)

        # 添加未来版本（从最大版本号+1开始，预置5个）
        # 确保至少包含v001到v005
        start_version = max(max_version_num + 1, 1)
        for i in range(start_version, start_version + 5):
            version = f"v{i:03d}"
            if version not in existing_versions:
                all_versions.append(version)

        # 按版本号降序排列（最新的在前）
        all_versions.sort(key=lambda x: int(x[1:]), reverse=True)
        return all_versions

    def update_file_list(self):
        """更新版本选择器"""
        try:
            self.version_selector.clear()
            versions = self.generate_version_list()
            existing_versions = self.get_existing_versions()

            # 找到最新的已存在版本
            latest_existing_version = existing_versions[0] if existing_versions else None

            for i, ver in enumerate(versions):
                filename = f"{self.base_name}_{ver}_mmq.ma"
                file_path = os.path.join(self.save_dir, filename)
                exists = os.path.exists(file_path)

                # 显示格式：版本号 - 文件名
                display_text = f"{ver} - {filename}"

                # 添加项目到下拉列表
                self.version_selector.addItem(display_text, ver)

                # 设置颜色
                try:
                    item = self.version_selector.model().item(i)
                    if exists:
                        if ver == latest_existing_version:
                            # 最新版本 - 绿色
                            from PySide2.QtGui import QColor
                            item.setForeground(QColor(46, 204, 113))  # 绿色
                        else:
                            # 历史版本 - 红色
                            from PySide2.QtGui import QColor
                            item.setForeground(QColor(231, 76, 60))   # 红色
                    else:
                        # 未创建版本 - 白色
                        from PySide2.QtGui import QColor
                        item.setForeground(QColor(255, 255, 255))     # 白色
                except:
                    pass  # 如果颜色设置失败，忽略错误

            # 默认选择逻辑：如果有已存在文件选择最新版本，否则选择第一版(v001)
            default_index = 0
            if latest_existing_version:
                # 有已存在文件，选择最新版本
                for i in range(self.version_selector.count()):
                    if self.version_selector.itemData(i) == latest_existing_version:
                        default_index = i
                        break
                self.log(f"默认选择最新版本: {latest_existing_version}")
            else:
                # 没有已存在文件，选择第一版(v001)
                for i in range(self.version_selector.count()):
                    if self.version_selector.itemData(i) == "v001":
                        default_index = i
                        break
                self.log("路径下无文件，默认选择第一版: v001")

            if self.version_selector.count() > 0:
                self.version_selector.setCurrentIndex(default_index)

            self.log(f"版本选择器已更新，共 {len(versions)} 个版本可选")

        except Exception as e:
            self.log(f"更新版本选择器错误: {e}")

    def on_version_selected(self, display_text):
        """版本选择改变时的回调"""
        if not display_text:
            return

        try:
            # 获取当前选择的版本号
            current_index = self.version_selector.currentIndex()
            version = self.version_selector.itemData(current_index)

            if version:
                filename = f"{self.base_name}_{version}_mmq.ma"
                file_path = os.path.join(self.save_dir, filename)
                exists = os.path.exists(file_path)

                # 获取现有版本列表来判断是否为最新版本
                existing_versions = self.get_existing_versions()
                latest_existing_version = existing_versions[0] if existing_versions else None

                # 设置当前选择框的颜色
                try:
                    from PySide2.QtGui import QColor
                    if exists:
                        if version == latest_existing_version:
                            # 最新版本 - 绿色
                            color = "color: #2ECC71;"  # 绿色
                            status_color = "🟢"
                        else:
                            # 历史版本 - 红色
                            color = "color: #E74C3C;"  # 红色
                            status_color = "🔴"
                    else:
                        # 未创建版本 - 白色
                        color = "color: #FFFFFF;"  # 白色
                        status_color = "⚪"

                    # 更新选择器的颜色
                    current_style = self.version_selector.styleSheet()
                    # 移除旧的颜色设置并添加新的
                    import re
                    new_style = re.sub(r'color:\s*[^;]+;', '', current_style)
                    new_style = new_style.replace('QComboBox {', f'QComboBox {{ {color}')
                    self.version_selector.setStyleSheet(new_style)
                except:
                    status_color = "●"

                status_text = f"当前选择: {status_color} {version}\n文件名: {filename}"

                if exists:
                    try:
                        size = os.path.getsize(file_path) / 1024 / 1024  # MB
                        mod_time = datetime.datetime.fromtimestamp(os.path.getmtime(file_path)).strftime("%Y-%m-%d %H:%M")
                        status_text += f"\n状态: 文件已存在\n大小: {size:.2f} MB\n修改时间: {mod_time}"
                    except:
                        status_text += "\n状态: 文件已存在"
                else:
                    status_text += "\n状态: 新版本 (文件不存在)"

                # 添加路径信息到同一个窗口
                status_text += f"\n\n📁 文件路径:\n{file_path}\n(点击此区域打开文件夹)"

                self.selection_info.setText(status_text)

                # 存储当前文件路径用于打开文件夹
                self.current_file_path = file_path

                self.log(f"选择版本: {version}")

        except Exception as e:
            self.log(f"版本选择回调错误: {e}")

    def open_file_location(self, event):
        """打开文件所在位置"""
        try:
            if hasattr(self, 'current_file_path') and self.current_file_path:
                import subprocess
                import platform

                # 获取文件所在目录
                directory = os.path.dirname(self.current_file_path)

                # 确保目录存在，如果不存在则创建
                if not os.path.exists(directory):
                    try:
                        os.makedirs(directory)
                        self.log(f"已创建目录: {directory}")
                    except Exception as e:
                        self.log(f"无法创建目录: {str(e)}")
                        return

                # 根据操作系统打开文件夹
                system = platform.system()
                if system == "Windows":
                    # Windows: 使用explorer打开目录
                    if os.path.exists(self.current_file_path):
                        # 如果文件存在，选中文件
                        subprocess.run(['explorer', '/select,', os.path.normpath(self.current_file_path)])
                    else:
                        # 如果文件不存在，只打开目录
                        subprocess.run(['explorer', os.path.normpath(directory)])
                elif system == "Darwin":  # macOS
                    if os.path.exists(self.current_file_path):
                        subprocess.run(['open', '-R', self.current_file_path])
                    else:
                        subprocess.run(['open', directory])
                else:  # Linux
                    subprocess.run(['xdg-open', directory])

                self.log(f"已打开文件位置: {directory}")
            else:
                # 如果没有选择文件，打开默认保存目录
                if os.path.exists(self.save_dir):
                    import subprocess
                    import platform
                    system = platform.system()
                    if system == "Windows":
                        subprocess.run(['explorer', os.path.normpath(self.save_dir)])
                    elif system == "Darwin":
                        subprocess.run(['open', self.save_dir])
                    else:
                        subprocess.run(['xdg-open', self.save_dir])
                    self.log(f"已打开默认保存目录: {self.save_dir}")
                else:
                    self.log("保存目录不存在")
        except Exception as e:
            self.log(f"打开文件位置失败: {str(e)}")

    def filename(self, ver):
        return f"{self.base_name}_{ver}_mmq.ma"

    def confirm_overwrite(self, path, ver):
        """确认覆盖文件"""
        if os.path.exists(path):
            reply = QMessageBox.question(self, "文件已存在",
                                       f"版本 {ver} 已存在，是否覆盖？\n\n{os.path.basename(path)}",
                                       QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.No:
                self.log("用户取消覆盖")
                return False
        return True

    def open_selected_file(self):
        """打开当前选择的文件"""
        try:
            # 获取当前选择的版本
            current_index = self.version_selector.currentIndex()
            if current_index < 0:
                QMessageBox.warning(self, "未选择文件", "请先选择要打开的文件版本")
                return

            version = self.version_selector.itemData(current_index)
            filename = self.filename(version)
            file_path = os.path.join(self.save_dir, filename)

            if not os.path.exists(file_path):
                QMessageBox.warning(self, "文件不存在", f"版本 {version} 的文件不存在:\n{filename}")
                return

            # 检查当前场景是否有未保存的更改
            if cmds.file(query=True, modified=True):
                reply = QMessageBox.question(self, "确认", "当前场景有未保存的更改，是否继续打开？",
                                           QMessageBox.Yes | QMessageBox.No)
                if reply == QMessageBox.No:
                    self.log("用户取消打开操作")
                    return

            # 打开文件
            cmds.file(file_path, open=True, force=True)
            self.log(f"成功打开: {filename}")
            QMessageBox.information(self, "打开成功", f"已打开版本 {version}:\n{filename}")

        except Exception as e:
            error_msg = f"打开文件失败: {str(e)}"
            self.log(error_msg)
            QMessageBox.critical(self, "打开失败", error_msg)

    def save_to_selected(self):
        """保存到当前选择的版本"""
        try:
            # 获取当前选择的版本
            current_index = self.version_selector.currentIndex()
            if current_index < 0:
                QMessageBox.warning(self, "未选择版本", "请先选择要保存的版本")
                return

            version = self.version_selector.itemData(current_index)
            filename = self.filename(version)
            file_path = os.path.join(self.save_dir, filename)

            # 创建目录（如果不存在）
            if not os.path.exists(self.save_dir):
                try:
                    os.makedirs(self.save_dir)
                    self.log(f"已创建目录: {self.save_dir}")
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"无法创建保存目录: {str(e)}")
                    return

            # 检查文件是否存在，询问覆盖
            if not self.confirm_overwrite(file_path, version):
                return

            # 保存文件
            self.log(f"正在保存到版本 {version}: {filename}")
            cmds.file(rename=file_path)
            cmds.file(save=True, type="mayaAscii")

            self.log(f"保存成功: {filename}")
            self.update_file_list()  # 刷新版本选择器
            QMessageBox.information(self, "保存成功", f"文件已保存为版本 {version}:\n{filename}")

        except Exception as e:
            error_msg = f"保存文件失败: {str(e)}"
            self.log(error_msg)
            QMessageBox.critical(self, "保存失败", error_msg)

# 启动逻辑
file_manager = None

def close_existing():
    """关闭已存在的窗口"""
    try:
        for name in ["MayaFileManagerCompactWorkspaceControl", "MayaFileManagerCompact"]:
            if (cmds.workspaceControl(name, exists=True) if "Workspace" in name else cmds.window(name, exists=True)):
                cmds.deleteUI(name)
                print(f"已删除: {name}")
    except: pass

def show_manager():
    """显示文件管理器"""
    global file_manager
    try:
        close_existing()
        if file_manager:
            try:
                file_manager.close()
                file_manager.deleteLater()
            except: pass
            file_manager = None

        file_manager = MayaFileManager()
        file_manager.show(dockable=True)
        print("✅ Maya文件管理器启动成功！")
        return file_manager
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return None

# 中文函数
重启文件管理器 = lambda: show_manager()
关闭现有窗口 = close_existing

# 立即启动
print("正在启动文件管理器...")
try:
    window = show_manager()
    if window:
        print("🎉 文件管理器界面已显示")
        print("📋 功能说明:")
        print("   • 文件列表显示最新9个版本")
        print("   • 上下滚动选择版本")
        print("   • Open按钮打开选中版本")
        print("   • Save按钮保存到选中版本")
    else:
        print("❌ 启动失败")
except Exception as e:
    print(f"❌ 启动异常: {e}")

print("=" * 50)
print("重启命令: show_manager() 或 重启文件管理器()")
print("=" * 50)
