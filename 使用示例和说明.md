# 通用文件管理器使用说明

## 📋 概述
通用文件管理器是一个可复用的版本控制工具，支持插件化架构，可以在不同软件中使用。

## 🏗️ 架构设计

### 核心组件
1. **`universal_file_manager.py`** - 通用文件管理器核心
2. **`FileManagerPlugin`** - 插件基类（抽象类）
3. **具体插件** - 各软件的具体实现

### 插件系统
```python
# 插件基类定义
class FileManagerPlugin(ABC):
    @abstractmethod
    def get_name(self):           # 插件名称
    def get_file_extension(self): # 文件扩展名
    def open_file(self, path):    # 打开文件
    def save_file(self, path):    # 保存文件
    def check_unsaved_changes(self): # 检查未保存更改
    def get_current_file_info(self): # 获取当前文件信息
```

## 🔌 已实现的插件

### 1. Maya插件 (`maya_plugin.py`)
- **文件格式**: `.ma` (Maya ASCII)
- **功能**: 完整的Maya场景文件管理
- **特色**: 支持Maya可停靠窗口
- **使用方法**:
```python
# 在Maya中运行
exec(open('maya_plugin.py').read())

# 或直接调用
from maya_plugin import show_maya_file_manager
show_maya_file_manager()
```

### 2. Blender插件 (`blender_plugin.py`)
- **文件格式**: `.blend`
- **功能**: Blender场景文件管理
- **使用方法**:
```python
# 在Blender中运行
exec(open('blender_plugin.py').read())
```

### 3. 文本编辑器插件 (`text_editor_plugin.py`)
- **文件格式**: `.txt`, `.py` 等
- **功能**: 通用文本文件管理
- **特色**: 支持多种文本格式，自动模板生成
- **使用方法**:
```python
# 独立运行
python text_editor_plugin.py

# 或在代码中调用
from text_editor_plugin import show_text_file_manager
show_text_file_manager(".py")  # Python文件
```

## 🚀 使用方法

### 基本使用
```python
from universal_file_manager import UniversalFileManager
from maya_plugin import MayaPlugin

# 创建插件实例
plugin = MayaPlugin()

# 创建文件管理器
manager = UniversalFileManager(
    plugin=plugin,
    save_dir="D:/Projects/Maya",
    base_name="my_project"
)

# 显示界面
manager.show()
```

### 动态切换插件
```python
# 创建不同插件
maya_plugin = MayaPlugin()
blender_plugin = BlenderPlugin()

# 切换插件
manager.set_plugin(maya_plugin)    # 切换到Maya
manager.set_plugin(blender_plugin) # 切换到Blender
```

## 🔧 自定义插件开发

### 创建新插件
```python
from universal_file_manager import FileManagerPlugin

class MyAppPlugin(FileManagerPlugin):
    def get_name(self):
        return "我的应用程序"
    
    def get_file_extension(self):
        return ".myapp"
    
    def open_file(self, file_path):
        # 实现打开文件的逻辑
        try:
            # 调用你的应用程序API
            my_app.open_file(file_path)
            return True
        except:
            return False
    
    def save_file(self, file_path):
        # 实现保存文件的逻辑
        try:
            my_app.save_file(file_path)
            return True
        except:
            return False
    
    def check_unsaved_changes(self):
        # 检查未保存更改
        return my_app.has_unsaved_changes()
    
    def get_current_file_info(self):
        # 返回当前文件信息
        return f"当前文件: {my_app.current_file}"
```

### 使用自定义插件
```python
# 创建插件实例
my_plugin = MyAppPlugin()

# 创建文件管理器
manager = UniversalFileManager(
    plugin=my_plugin,
    save_dir="/path/to/projects",
    base_name="project"
)

manager.show()
```

## 🎨 界面功能

### 核心功能
- **iPhone式版本选择器**: 滚动选择版本
- **颜色区分系统**: 绿色(最新)、红色(历史)、白色(新版本)
- **可调整界面**: 信息区和日志区可手动调整大小
- **智能默认选择**: 自动选择最新版本或v001
- **跨平台文件夹打开**: 点击路径打开文件夹

### 配置选项
- **保存目录**: 可自定义项目保存路径
- **项目名称**: 可自定义文件名前缀
- **版本管理**: 自动生成v001-v999版本号

## 📁 文件命名规则
```
{项目名称}_{版本号}{扩展名}
例如:
- my_project_v001.ma
- blender_scene_v002.blend
- python_script_v003.py
```

## 🔄 版本控制特性

### 版本生成规则
- 扫描现有文件，找到最高版本号
- 在最高版本基础上预置5个未来版本
- 确保至少包含v001-v005

### 颜色编码
- 🟢 **绿色**: 最新已存在版本（推荐打开）
- 🔴 **红色**: 历史版本（旧版本）
- ⚪ **白色**: 未创建版本（可保存新版本）

## 🛠️ 高级功能

### 插件热切换
```python
# 运行时切换插件
manager.set_plugin(new_plugin)
```

### 批量操作
```python
# 获取所有版本
versions = manager.get_existing_versions()

# 批量处理
for version in versions:
    file_path = manager.get_file_path(version)
    # 处理文件...
```

### 自定义配置
```python
# 自定义保存目录和项目名
manager = UniversalFileManager(
    plugin=plugin,
    save_dir="/custom/path",
    base_name="custom_project"
)

# 运行时修改配置
manager.save_dir = "/new/path"
manager.base_name = "new_project"
manager.update_file_list()
```

## 🚀 部署方式

### 1. 独立应用
```bash
python text_editor_plugin.py
```

### 2. 软件内嵌
```python
# 在目标软件中运行
exec(open('maya_plugin.py').read())
```

### 3. 模块导入
```python
from universal_file_manager import create_file_manager
from maya_plugin import MayaPlugin

manager = create_file_manager(MayaPlugin())
```

## 📦 依赖要求

### 核心依赖
- Python 3.6+
- PySide2 或 PyQt5

### 软件特定依赖
- **Maya**: maya.cmds, maya.app.general.mayaMixin
- **Blender**: bpy
- **文本编辑器**: 无额外依赖

## 🔍 故障排除

### 常见问题
1. **插件不可用**: 检查对应软件是否正确安装
2. **界面不显示**: 确认Qt库正确安装
3. **文件操作失败**: 检查文件路径权限

### 调试模式
```python
# 启用详细日志
manager.log("调试信息")

# 检查插件状态
print(f"插件: {manager.plugin.get_name()}")
print(f"扩展名: {manager.plugin.get_file_extension()}")
```

## 🎯 最佳实践

1. **插件开发**: 始终实现完整的错误处理
2. **文件命名**: 使用有意义的项目名称
3. **版本管理**: 定期清理旧版本文件
4. **路径设置**: 使用绝对路径避免混乱
5. **备份策略**: 重要项目建议额外备份
