# -*- coding: utf-8 -*-
"""Blender文件管理器插件 | 作者: MMQ"""

import os
from universal_file_manager import FileManagerPlugin

try:
    import bpy
    BLENDER_AVAILABLE = True
    print("✅ Blender模块导入成功")
except ImportError:
    BLENDER_AVAILABLE = False
    print("❌ Blender模块不可用")

class BlenderPlugin(FileManagerPlugin):
    """Blender文件管理器插件"""
    
    def get_name(self):
        """返回插件名称"""
        return "Blender"
    
    def get_file_extension(self):
        """返回文件扩展名"""
        return ".blend"
    
    def open_file(self, file_path):
        """打开Blender文件"""
        if not BLENDER_AVAILABLE:
            print("❌ Blender不可用")
            return False
        
        try:
            bpy.ops.wm.open_mainfile(filepath=file_path)
            print(f"✅ 成功打开Blender文件: {os.path.basename(file_path)}")
            return True
        except Exception as e:
            print(f"❌ 打开Blender文件失败: {e}")
            return False
    
    def save_file(self, file_path):
        """保存Blender文件"""
        if not BLENDER_AVAILABLE:
            print("❌ Blender不可用")
            return False
        
        try:
            bpy.ops.wm.save_as_mainfile(filepath=file_path)
            print(f"✅ 成功保存Blender文件: {os.path.basename(file_path)}")
            return True
        except Exception as e:
            print(f"❌ 保存Blender文件失败: {e}")
            return False
    
    def check_unsaved_changes(self):
        """检查是否有未保存的更改"""
        if not BLENDER_AVAILABLE:
            return False
        
        try:
            return bpy.data.is_dirty
        except:
            return False
    
    def get_current_file_info(self):
        """获取当前文件信息"""
        if not BLENDER_AVAILABLE:
            return "Blender不可用"
        
        try:
            current_file = bpy.data.filepath
            if current_file:
                return f"当前文件: {os.path.basename(current_file)}"
            else:
                return "当前文件: 未保存的场景"
        except:
            return "无法获取文件信息"

def show_blender_file_manager(save_dir=None, base_name=None):
    """显示Blender文件管理器"""
    if not BLENDER_AVAILABLE:
        print("❌ Blender不可用，无法启动Blender文件管理器")
        return None
    
    try:
        # 导入通用文件管理器
        from universal_file_manager import UniversalFileManager
        
        # 创建Blender插件
        blender_plugin = BlenderPlugin()
        
        # 创建文件管理器
        manager = UniversalFileManager(
            plugin=blender_plugin,
            save_dir=save_dir or os.path.expanduser("~/Documents/Blender_Projects"),
            base_name=base_name or "blender_project"
        )
        
        manager.show()
        print("✅ Blender文件管理器启动成功！")
        return manager
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return None

# 中文函数别名
启动Blender文件管理器 = lambda: show_blender_file_manager()

if __name__ == "__main__":
    show_blender_file_manager()
