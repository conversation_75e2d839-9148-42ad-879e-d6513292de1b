# -*- coding: utf-8 -*-
"""Maya菜单管理器 | 作者: MMQ"""

import os
from menus.base_menu import MenuBase

class MayaMenu(MenuBase):
    """Maya菜单管理器"""
    
    def __init__(self):
        self.menu_name = "FileManagerMenu"
        self.menu_label = "文件管理器"
        self.available = False
        
        try:
            import maya.cmds as cmds
            import maya.mel as mel
            self.cmds = cmds
            self.mel = mel
            self.available = True
        except ImportError:
            pass
    
    def create_menu(self, controller=None):
        """创建Maya菜单"""
        if not self.available:
            return False
        
        try:
            # 获取主菜单栏
            main_window = self.mel.eval('$temp1=$gMainWindow')
            
            # 删除已存在的菜单
            if self.cmds.menu(self.menu_name, exists=True):
                self.cmds.deleteUI(self.menu_name)
            
            # 创建新菜单
            file_manager_menu = self.cmds.menu(
                self.menu_name,
                label=self.menu_label,
                parent=main_window,
                tearOff=True
            )
            
            # 添加菜单项
            self.cmds.menuItem(
                label="打开文件管理器",
                command=self._get_open_command(controller),
                parent=file_manager_menu,
                image="fileOpen.png"
            )
            
            self.cmds.menuItem(divider=True, parent=file_manager_menu)
            
            self.cmds.menuItem(
                label="新建版本",
                command=self._get_new_version_command(controller),
                parent=file_manager_menu,
                image="fileNew.png"
            )
            
            self.cmds.menuItem(
                label="保存版本",
                command=self._get_save_command(controller),
                parent=file_manager_menu,
                image="fileSave.png"
            )
            
            self.cmds.menuItem(
                label="打开最新版本",
                command=self._get_open_latest_command(controller),
                parent=file_manager_menu,
                image="fileOpen.png"
            )
            
            self.cmds.menuItem(divider=True, parent=file_manager_menu)
            
            self.cmds.menuItem(
                label="打开项目文件夹",
                command=self._get_open_folder_command(controller),
                parent=file_manager_menu,
                image="folder-open.png"
            )
            
            self.cmds.menuItem(divider=True, parent=file_manager_menu)
            
            self.cmds.menuItem(
                label="设置",
                command=self._get_settings_command(controller),
                parent=file_manager_menu,
                image="advancedSettings.png"
            )
            
            self.cmds.menuItem(
                label="关于",
                command=self._get_about_command(),
                parent=file_manager_menu,
                image="help.png"
            )
            
            print(f"✅ Maya菜单创建成功: {self.menu_label}")
            return True
            
        except Exception as e:
            print(f"❌ Maya菜单创建失败: {e}")
            return False
    
    def remove_menu(self):
        """移除Maya菜单"""
        if not self.available:
            return False
        
        try:
            if self.cmds.menu(self.menu_name, exists=True):
                self.cmds.deleteUI(self.menu_name)
                print(f"✅ Maya菜单移除成功: {self.menu_label}")
                return True
        except Exception as e:
            print(f"❌ Maya菜单移除失败: {e}")
        return False
    
    def _get_open_command(self, controller):
        """获取打开文件管理器命令"""
        if controller:
            return lambda *args: controller.show()
        else:
            return 'python("exec(open(\'main.py\').read())")'
    
    def _get_new_version_command(self, controller):
        """获取新建版本命令"""
        if controller:
            return lambda *args: controller.create_new_version()
        else:
            return 'python("print(\'新建版本\')")'
    
    def _get_save_command(self, controller):
        """获取保存命令"""
        if controller:
            return lambda *args: controller.save_current_version()
        else:
            return 'python("print(\'保存版本\')")'
    
    def _get_open_latest_command(self, controller):
        """获取打开最新版本命令"""
        if controller:
            return lambda *args: controller.open_latest_version()
        else:
            return 'python("print(\'打开最新版本\')")'
    
    def _get_open_folder_command(self, controller):
        """获取打开文件夹命令"""
        if controller:
            return lambda *args: controller.open_project_folder()
        else:
            return 'python("print(\'打开项目文件夹\')")'
    
    def _get_settings_command(self, controller):
        """获取设置命令"""
        if controller:
            return lambda *args: controller.show_settings()
        else:
            return 'python("print(\'设置\')")'
    
    def _get_about_command(self):
        """获取关于命令"""
        return lambda *args: self._show_about_dialog()
    
    def _show_about_dialog(self):
        """显示关于对话框"""
        if self.available:
            self.cmds.confirmDialog(
                title="关于文件管理器",
                message="文件管理器 v1.0\n\n通用版本控制工具\n支持Maya、Blender等DCC软件\n\n作者: MMQ",
                button=["确定"],
                defaultButton="确定"
            )
    
    def update_menu_state(self, controller):
        """更新菜单状态"""
        # 这里可以根据控制器状态更新菜单项的启用/禁用状态
        pass
    
    def add_custom_menu_item(self, label, command, icon=None, parent=None):
        """添加自定义菜单项"""
        if not self.available:
            return False
        
        try:
            if parent is None:
                parent = self.menu_name
            
            self.cmds.menuItem(
                label=label,
                command=command,
                parent=parent,
                image=icon or ""
            )
            return True
        except Exception as e:
            print(f"添加菜单项失败: {e}")
            return False
