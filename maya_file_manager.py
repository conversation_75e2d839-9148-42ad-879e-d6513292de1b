# -*- coding: utf-8 -*-
"""
Maya文件管理器插件
作者: MMQ
版本: 1.0
兼容性: Maya 2020+

这个插件提供了一个用户友好的文件管理界面，支持打开和保存Maya文件。
"""

import os
import sys
import logging
from functools import partial

try:
    from PySide2.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                                   QLabel, QFileDialog, QMessageBox, QTextEdit,
                                   QSplitter, QFrame)
    from PySide2.QtCore import Qt, Signal
    from PySide2.QtGui import QFont, QIcon
except ImportError:
    raise ImportError("PySide2 未找到。请确保Maya版本支持PySide2。")

try:
    import maya.cmds as cmds
    import maya.mel as mel
    from maya.app.general.mayaMixin import MayaQWidgetDockableMixin
    import maya.OpenMayaUI as omui
except ImportError:
    raise ImportError("Maya API 未找到。请在Maya环境中运行此脚本。")

# 设置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FileManagerWidget(MayaQWidgetDockableMixin, QWidget):
    """
    Maya文件管理器主界面类
    继承自MayaQWidgetDockableMixin以支持停靠功能
    """

    # 定义信号
    file_opened = Signal(str)
    file_saved = Signal(str)

    def __init__(self, parent=None):
        """初始化文件管理器界面"""
        super(FileManagerWidget, self).__init__(parent)

        # 设置窗口属性
        self.setWindowTitle("Maya文件管理器")
        self.setMinimumSize(400, 300)
        self.setObjectName("MayaFileManagerWidget")

        # 支持的文件格式
        self.supported_formats = {
            'Maya ASCII (*.ma)': '*.ma',
            'Maya Binary (*.mb)': '*.mb',
            'All Maya Files (*.ma *.mb)': '*.ma *.mb',
            'All Files (*.*)': '*.*'
        }

        # 创建界面
        self.create_ui()
        self.setup_connections()

        # 记录日志
        logger.info("文件管理器界面初始化完成")

    def create_ui(self):
        """创建用户界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 标题标签
        title_label = QLabel("Maya文件管理器")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(line)

        # 按钮区域
        button_layout = QVBoxLayout()
        button_layout.setSpacing(15)

        # 打开文件按钮
        self.open_button = QPushButton("打开文件")
        self.open_button.setMinimumHeight(40)
        self.open_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        button_layout.addWidget(self.open_button)

        # 保存文件按钮
        self.save_button = QPushButton("保存文件")
        self.save_button.setMinimumHeight(40)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
        """)
        button_layout.addWidget(self.save_button)

        main_layout.addLayout(button_layout)

        # 状态信息区域
        status_label = QLabel("状态信息:")
        status_label.setFont(QFont("Arial", 10, QFont.Bold))
        main_layout.addWidget(status_label)

        # 状态文本框
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(120)
        self.status_text.setReadOnly(True)
        self.status_text.setStyleSheet("""
            QTextEdit {
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 3px;
                padding: 5px;
                font-family: Consolas, monospace;
                font-size: 9px;
            }
        """)
        main_layout.addWidget(self.status_text)

        # 添加弹性空间
        main_layout.addStretch()

        # 底部信息
        info_label = QLabel("提示: 支持 .ma 和 .mb 格式的Maya文件")
        info_label.setStyleSheet("color: #666; font-size: 10px;")
        info_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(info_label)

    def setup_connections(self):
        """设置信号连接"""
        self.open_button.clicked.connect(self.open_file_dialog)
        self.save_button.clicked.connect(self.save_file_dialog)

        # 连接自定义信号
        self.file_opened.connect(self.on_file_opened)
        self.file_saved.connect(self.on_file_saved)

    def log_message(self, message, level="INFO"):
        """在状态文本框中记录消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {level}: {message}"

        self.status_text.append(formatted_message)

        # 记录到Python日志
        if level == "ERROR":
            logger.error(message)
        elif level == "WARNING":
            logger.warning(message)
        else:
            logger.info(message)

    def open_file_dialog(self):
        """打开文件对话框"""
        try:
            self.log_message("正在打开文件选择对话框...")

            # 获取当前Maya工作目录
            current_workspace = cmds.workspace(query=True, rootDirectory=True)

            # 构建文件过滤器
            file_filter = ";;".join(self.supported_formats.keys())

            # 打开文件对话框
            file_path, selected_filter = QFileDialog.getOpenFileName(
                self,
                "选择Maya文件",
                current_workspace,
                file_filter
            )

            if file_path:
                self.open_maya_file(file_path)
            else:
                self.log_message("用户取消了文件选择")

        except Exception as e:
            error_msg = f"打开文件对话框时发生错误: {str(e)}"
            self.log_message(error_msg, "ERROR")
            self.show_error_message("错误", error_msg)

    def save_file_dialog(self):
        """保存文件对话框"""
        try:
            self.log_message("正在打开文件保存对话框...")

            # 获取当前Maya工作目录
            current_workspace = cmds.workspace(query=True, rootDirectory=True)

            # 获取当前场景名称作为默认文件名
            current_scene = cmds.file(query=True, sceneName=True)
            if current_scene:
                default_name = os.path.splitext(os.path.basename(current_scene))[0]
            else:
                default_name = "untitled"

            default_path = os.path.join(current_workspace, default_name + ".ma")

            # 构建文件过滤器（保存时只显示Maya格式）
            save_filters = {
                'Maya ASCII (*.ma)': '*.ma',
                'Maya Binary (*.mb)': '*.mb'
            }
            file_filter = ";;".join(save_filters.keys())

            # 打开保存对话框
            file_path, selected_filter = QFileDialog.getSaveFileName(
                self,
                "保存Maya文件",
                default_path,
                file_filter
            )

            if file_path:
                self.save_maya_file(file_path, selected_filter)
            else:
                self.log_message("用户取消了文件保存")

        except Exception as e:
            error_msg = f"打开保存对话框时发生错误: {str(e)}"
            self.log_message(error_msg, "ERROR")
            self.show_error_message("错误", error_msg)

    def open_maya_file(self, file_path):
        """打开Maya文件"""
        try:
            # 验证文件路径
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")

            # 验证文件格式
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in ['.ma', '.mb']:
                raise ValueError(f"不支持的文件格式: {file_ext}")

            self.log_message(f"正在打开文件: {file_path}")

            # 检查当前场景是否有未保存的更改
            if cmds.file(query=True, modified=True):
                reply = QMessageBox.question(
                    self,
                    "确认",
                    "当前场景有未保存的更改。是否继续打开新文件？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                if reply == QMessageBox.No:
                    self.log_message("用户取消了文件打开操作")
                    return

            # 打开文件
            cmds.file(file_path, open=True, force=True)

            success_msg = f"成功打开文件: {os.path.basename(file_path)}"
            self.log_message(success_msg)
            self.show_success_message("成功", success_msg)

            # 发射信号
            self.file_opened.emit(file_path)

        except FileNotFoundError as e:
            error_msg = f"文件未找到: {str(e)}"
            self.log_message(error_msg, "ERROR")
            self.show_error_message("文件错误", error_msg)

        except ValueError as e:
            error_msg = f"文件格式错误: {str(e)}"
            self.log_message(error_msg, "ERROR")
            self.show_error_message("格式错误", error_msg)

        except Exception as e:
            error_msg = f"打开文件时发生未知错误: {str(e)}"
            self.log_message(error_msg, "ERROR")
            self.show_error_message("错误", error_msg)

    def save_maya_file(self, file_path, selected_filter):
        """保存Maya文件"""
        try:
            # 确定文件类型
            if "ASCII" in selected_filter:
                file_type = "mayaAscii"
                if not file_path.endswith('.ma'):
                    file_path += '.ma'
            else:
                file_type = "mayaBinary"
                if not file_path.endswith('.mb'):
                    file_path += '.mb'

            self.log_message(f"正在保存文件: {file_path} (格式: {file_type})")

            # 检查目录权限
            directory = os.path.dirname(file_path)
            if not os.access(directory, os.W_OK):
                raise PermissionError(f"没有写入权限: {directory}")

            # 保存文件
            cmds.file(rename=file_path)
            cmds.file(save=True, type=file_type)

            success_msg = f"成功保存文件: {os.path.basename(file_path)}"
            self.log_message(success_msg)
            self.show_success_message("成功", success_msg)

            # 发射信号
            self.file_saved.emit(file_path)

        except PermissionError as e:
            error_msg = f"权限错误: {str(e)}"
            self.log_message(error_msg, "ERROR")
            self.show_error_message("权限错误", error_msg)

        except Exception as e:
            error_msg = f"保存文件时发生错误: {str(e)}"
            self.log_message(error_msg, "ERROR")
            self.show_error_message("错误", error_msg)

    def show_success_message(self, title, message):
        """显示成功消息"""
        msg_box = QMessageBox(self)
        msg_box.setIcon(QMessageBox.Information)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.exec_()

    def show_error_message(self, title, message):
        """显示错误消息"""
        msg_box = QMessageBox(self)
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.exec_()

    def on_file_opened(self, file_path):
        """文件打开后的回调"""
        self.log_message(f"文件打开事件: {file_path}")

    def on_file_saved(self, file_path):
        """文件保存后的回调"""
        self.log_message(f"文件保存事件: {file_path}")


# 全局变量存储窗口实例
file_manager_window = None


def show_file_manager():
    """显示文件管理器窗口"""
    global file_manager_window

    try:
        # 如果窗口已存在，则显示它
        if file_manager_window is not None:
            file_manager_window.show()
            file_manager_window.raise_()
            return file_manager_window

        # 创建新窗口
        file_manager_window = FileManagerWidget()
        file_manager_window.show(dockable=True)

        logger.info("文件管理器窗口已创建并显示")
        return file_manager_window

    except Exception as e:
        error_msg = f"显示文件管理器时发生错误: {str(e)}"
        logger.error(error_msg)
        cmds.warning(error_msg)
        return None


def close_file_manager():
    """关闭文件管理器窗口"""
    global file_manager_window

    try:
        if file_manager_window is not None:
            file_manager_window.close()
            file_manager_window = None
            logger.info("文件管理器窗口已关闭")

    except Exception as e:
        error_msg = f"关闭文件管理器时发生错误: {str(e)}"
        logger.error(error_msg)
        cmds.warning(error_msg)


def install_menu():
    """在Maya菜单栏中安装文件管理器菜单"""
    try:
        # 检查菜单是否已存在
        if cmds.menu("FileManagerMenu", exists=True):
            cmds.deleteUI("FileManagerMenu")

        # 创建菜单
        main_window = mel.eval('$temp1=$gMainWindow')
        file_manager_menu = cmds.menu(
            "FileManagerMenu",
            label="文件管理器",
            parent=main_window,
            tearOff=True
        )

        # 添加菜单项
        cmds.menuItem(
            label="打开文件管理器",
            command="import maya_file_manager; maya_file_manager.show_file_manager()",
            parent=file_manager_menu
        )

        cmds.menuItem(divider=True, parent=file_manager_menu)

        cmds.menuItem(
            label="关闭文件管理器",
            command="import maya_file_manager; maya_file_manager.close_file_manager()",
            parent=file_manager_menu
        )

        logger.info("文件管理器菜单已安装")

    except Exception as e:
        error_msg = f"安装菜单时发生错误: {str(e)}"
        logger.error(error_msg)
        cmds.warning(error_msg)


def uninstall_menu():
    """卸载文件管理器菜单"""
    try:
        if cmds.menu("FileManagerMenu", exists=True):
            cmds.deleteUI("FileManagerMenu")
            logger.info("文件管理器菜单已卸载")

    except Exception as e:
        error_msg = f"卸载菜单时发生错误: {str(e)}"
        logger.error(error_msg)


# 插件初始化
def initialize_plugin():
    """初始化插件"""
    try:
        install_menu()
        logger.info("Maya文件管理器插件初始化完成")
        cmds.warning("Maya文件管理器插件已加载。请在菜单栏中查找'文件管理器'菜单。")

    except Exception as e:
        error_msg = f"初始化插件时发生错误: {str(e)}"
        logger.error(error_msg)
        cmds.warning(error_msg)


def uninitialize_plugin():
    """卸载插件"""
    try:
        close_file_manager()
        uninstall_menu()
        logger.info("Maya文件管理器插件已卸载")

    except Exception as e:
        error_msg = f"卸载插件时发生错误: {str(e)}"
        logger.error(error_msg)


# 如果直接运行此脚本，则显示文件管理器
if __name__ == "__main__":
    show_file_manager()
