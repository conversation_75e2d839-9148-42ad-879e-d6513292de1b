#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""通用文件管理器演示示例 | 作者: MMQ"""

import os
import sys

def main():
    """主演示函数"""
    print("🚀 通用文件管理器演示")
    print("=" * 50)
    
    # 检查Qt库
    try:
        from PySide2.QtWidgets import QApplication
        print("✅ PySide2可用")
    except ImportError:
        try:
            from PyQt5.QtWidgets import QApplication
            print("✅ PyQt5可用")
        except ImportError:
            print("❌ 需要安装PySide2或PyQt5")
            return
    
    # 创建应用程序
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    # 演示不同插件
    demo_plugins(app)

def demo_plugins(app):
    """演示不同插件的使用"""
    
    print("\n📋 可用插件演示:")
    print("1. Maya插件")
    print("2. Blender插件") 
    print("3. 文本编辑器插件")
    print("4. Python脚本插件")
    print("5. 自定义插件示例")
    
    choice = input("\n请选择要演示的插件 (1-5): ").strip()
    
    if choice == "1":
        demo_maya_plugin(app)
    elif choice == "2":
        demo_blender_plugin(app)
    elif choice == "3":
        demo_text_plugin(app)
    elif choice == "4":
        demo_python_plugin(app)
    elif choice == "5":
        demo_custom_plugin(app)
    else:
        print("❌ 无效选择")
        return
    
    # 启动事件循环
    print("\n🎉 文件管理器已启动，关闭窗口退出演示")
    sys.exit(app.exec_())

def demo_maya_plugin(app):
    """演示Maya插件"""
    print("\n🎯 Maya插件演示")
    
    try:
        from maya_plugin import MayaPlugin, show_standalone_manager
        
        # 创建Maya插件
        plugin = MayaPlugin()
        print(f"插件名称: {plugin.get_name()}")
        print(f"文件扩展名: {plugin.get_file_extension()}")
        
        # 启动独立管理器（不依赖Maya）
        manager = show_standalone_manager(
            save_dir=os.path.expanduser("~/Documents/Maya_Demo"),
            base_name="maya_demo_project"
        )
        
        if manager:
            print("✅ Maya文件管理器演示启动成功")
        else:
            print("❌ 启动失败")
            
    except Exception as e:
        print(f"❌ Maya插件演示失败: {e}")

def demo_blender_plugin(app):
    """演示Blender插件"""
    print("\n🎯 Blender插件演示")
    
    try:
        from blender_plugin import BlenderPlugin
        from universal_file_manager import UniversalFileManager
        
        # 创建Blender插件
        plugin = BlenderPlugin()
        print(f"插件名称: {plugin.get_name()}")
        print(f"文件扩展名: {plugin.get_file_extension()}")
        
        # 创建文件管理器
        manager = UniversalFileManager(
            plugin=plugin,
            save_dir=os.path.expanduser("~/Documents/Blender_Demo"),
            base_name="blender_demo_project"
        )
        
        manager.show()
        print("✅ Blender文件管理器演示启动成功")
        
    except Exception as e:
        print(f"❌ Blender插件演示失败: {e}")

def demo_text_plugin(app):
    """演示文本编辑器插件"""
    print("\n🎯 文本编辑器插件演示")
    
    try:
        from text_editor_plugin import TextEditorPlugin
        from universal_file_manager import UniversalFileManager
        
        # 创建文本编辑器插件
        plugin = TextEditorPlugin(".txt")
        print(f"插件名称: {plugin.get_name()}")
        print(f"文件扩展名: {plugin.get_file_extension()}")
        
        # 创建文件管理器
        manager = UniversalFileManager(
            plugin=plugin,
            save_dir=os.path.expanduser("~/Documents/Text_Demo"),
            base_name="text_demo_project"
        )
        
        manager.show()
        print("✅ 文本编辑器文件管理器演示启动成功")
        
    except Exception as e:
        print(f"❌ 文本编辑器插件演示失败: {e}")

def demo_python_plugin(app):
    """演示Python脚本插件"""
    print("\n🎯 Python脚本插件演示")
    
    try:
        from text_editor_plugin import PythonScriptPlugin
        from universal_file_manager import UniversalFileManager
        
        # 创建Python脚本插件
        plugin = PythonScriptPlugin()
        print(f"插件名称: {plugin.get_name()}")
        print(f"文件扩展名: {plugin.get_file_extension()}")
        
        # 创建文件管理器
        manager = UniversalFileManager(
            plugin=plugin,
            save_dir=os.path.expanduser("~/Documents/Python_Demo"),
            base_name="python_demo_script"
        )
        
        manager.show()
        print("✅ Python脚本文件管理器演示启动成功")
        
    except Exception as e:
        print(f"❌ Python脚本插件演示失败: {e}")

def demo_custom_plugin(app):
    """演示自定义插件"""
    print("\n🎯 自定义插件演示")
    
    try:
        from universal_file_manager import FileManagerPlugin, UniversalFileManager
        
        # 创建自定义插件
        class DemoPlugin(FileManagerPlugin):
            def get_name(self):
                return "演示插件"
            
            def get_file_extension(self):
                return ".demo"
            
            def open_file(self, file_path):
                print(f"📂 模拟打开文件: {file_path}")
                return True
            
            def save_file(self, file_path):
                try:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(f"# 演示文件\n")
                        f.write(f"# 文件路径: {file_path}\n")
                        f.write(f"# 这是一个演示插件创建的文件\n")
                    print(f"💾 模拟保存文件: {file_path}")
                    return True
                except Exception as e:
                    print(f"❌ 保存失败: {e}")
                    return False
            
            def check_unsaved_changes(self):
                return False  # 演示插件无未保存检查
            
            def get_current_file_info(self):
                return "演示插件 - 当前无文件"
        
        # 创建插件实例
        plugin = DemoPlugin()
        print(f"插件名称: {plugin.get_name()}")
        print(f"文件扩展名: {plugin.get_file_extension()}")
        
        # 创建文件管理器
        manager = UniversalFileManager(
            plugin=plugin,
            save_dir=os.path.expanduser("~/Documents/Demo_Plugin"),
            base_name="demo_project"
        )
        
        manager.show()
        print("✅ 自定义插件文件管理器演示启动成功")
        print("💡 提示: 这个演示插件会创建.demo文件并用默认程序打开")
        
    except Exception as e:
        print(f"❌ 自定义插件演示失败: {e}")

def show_plugin_comparison():
    """显示插件对比"""
    print("\n📊 插件功能对比:")
    print("=" * 80)
    print(f"{'插件名称':<15} {'文件格式':<10} {'软件依赖':<15} {'特色功能':<30}")
    print("-" * 80)
    print(f"{'Maya插件':<15} {'.ma':<10} {'Maya':<15} {'可停靠窗口，完整Maya集成':<30}")
    print(f"{'Blender插件':<15} {'.blend':<10} {'Blender':<15} {'Blender场景管理':<30}")
    print(f"{'文本编辑器':<15} {'.txt':<10} {'无':<15} {'通用文本文件，跨平台':<30}")
    print(f"{'Python脚本':<15} {'.py':<10} {'无':<15} {'Python模板，语法高亮':<30}")
    print(f"{'自定义插件':<15} {'自定义':<10} {'自定义':<15} {'完全可定制':<30}")
    print("=" * 80)

if __name__ == "__main__":
    # 显示插件对比
    show_plugin_comparison()
    
    # 运行主演示
    main()
