# -*- coding: utf-8 -*-
"""
Maya文件管理器核心功能测试脚本
作者: MMQ
版本: 1.0

这是一个独立的测试脚本，可以直接在Maya脚本编辑器中运行
用于测试和验证Maya文件管理器的核心功能
"""

import os
import sys
import platform
import datetime

def print_separator(title="", char="=", width=60):
    """打印分隔线，用于美化输出"""
    if title:
        title_line = f" {title} "
        padding = (width - len(title_line)) // 2
        line = char * padding + title_line + char * padding
        if len(line) < width:
            line += char
    else:
        line = char * width
    print(line)

def get_maya_scripts_path():
    """
    获取Maya scripts目录路径
    这是从install_plugin.py提取的核心函数
    """
    try:
        # 尝试导入Maya模块来获取路径
        import maya.cmds as cmds
        maya_app_dir = cmds.internalVar(userAppDir=True)
        scripts_dir = os.path.join(maya_app_dir, "scripts")
        return scripts_dir, "Maya环境"
    except ImportError:
        # 如果不在Maya环境中，使用默认路径
        system = platform.system()

        if system == "Windows":
            # Windows路径
            maya_versions = ["2024", "2023", "2022", "2021", "2020"]
            documents_path = os.path.expanduser("~/Documents")

            for version in maya_versions:
                maya_path = os.path.join(documents_path, f"maya/{version}/scripts")
                if os.path.exists(maya_path):
                    return maya_path, f"Windows默认路径 (Maya {version})"

            # 如果没找到，返回最新版本的路径
            default_path = os.path.join(documents_path, "maya/2024/scripts")
            return default_path, "Windows默认路径 (Maya 2024)"

        elif system == "Darwin":  # macOS
            maya_versions = ["2024", "2023", "2022", "2021", "2020"]
            home_path = os.path.expanduser("~")

            for version in maya_versions:
                maya_path = os.path.join(home_path, f"Library/Preferences/Autodesk/maya/{version}/scripts")
                if os.path.exists(maya_path):
                    return maya_path, f"macOS默认路径 (Maya {version})"

            default_path = os.path.join(home_path, "Library/Preferences/Autodesk/maya/2024/scripts")
            return default_path, "macOS默认路径 (Maya 2024)"

        else:  # Linux
            maya_versions = ["2024", "2023", "2022", "2021", "2020"]
            home_path = os.path.expanduser("~")

            for version in maya_versions:
                maya_path = os.path.join(home_path, f"maya/{version}/scripts")
                if os.path.exists(maya_path):
                    return maya_path, f"Linux默认路径 (Maya {version})"

            default_path = os.path.join(home_path, "maya/2024/scripts")
            return default_path, "Linux默认路径 (Maya 2024)"

def test_maya_environment():
    """测试Maya环境和API可用性"""
    print_separator("Maya环境测试")

    # 测试Maya cmds模块
    try:
        import maya.cmds as cmds
        print("✓ maya.cmds 模块导入成功")

        # 测试基本Maya命令
        try:
            maya_version = cmds.about(version=True)
            print(f"✓ Maya版本: {maya_version}")
        except Exception as e:
            print(f"✗ 获取Maya版本失败: {e}")

        try:
            current_scene = cmds.file(query=True, sceneName=True)
            if current_scene:
                print(f"✓ 当前场景: {os.path.basename(current_scene)}")
            else:
                print("✓ 当前场景: 新建场景 (未保存)")
        except Exception as e:
            print(f"✗ 获取当前场景信息失败: {e}")

    except ImportError:
        print("✗ maya.cmds 模块导入失败 - 不在Maya环境中")
        return False

    # 测试Maya mel模块
    try:
        import maya.mel as mel
        print("✓ maya.mel 模块导入成功")
    except ImportError:
        print("✗ maya.mel 模块导入失败")

    # 测试PySide2
    try:
        from PySide2.QtWidgets import QWidget
        print("✓ PySide2 模块导入成功")
    except ImportError:
        print("✗ PySide2 模块导入失败")

    return True

def test_scripts_path_function():
    """测试get_maya_scripts_path函数"""
    print_separator("Scripts路径测试")

    try:
        scripts_path, source = get_maya_scripts_path()
        print(f"✓ Scripts路径获取成功")
        print(f"  路径: {scripts_path}")
        print(f"  来源: {source}")

        # 检查路径是否存在
        if os.path.exists(scripts_path):
            print(f"✓ 路径存在")

            # 检查是否可写
            if os.access(scripts_path, os.W_OK):
                print(f"✓ 路径可写")
            else:
                print(f"✗ 路径不可写")

            # 列出目录内容
            try:
                files = os.listdir(scripts_path)
                python_files = [f for f in files if f.endswith('.py')]
                print(f"✓ 目录中的Python文件数量: {len(python_files)}")
                if python_files:
                    print(f"  示例文件: {python_files[:3]}")  # 显示前3个文件
            except Exception as e:
                print(f"✗ 无法读取目录内容: {e}")
        else:
            print(f"✗ 路径不存在")

            # 尝试创建目录
            try:
                os.makedirs(scripts_path, exist_ok=True)
                print(f"✓ 成功创建目录")
            except Exception as e:
                print(f"✗ 创建目录失败: {e}")

        return True, scripts_path

    except Exception as e:
        print(f"✗ 获取Scripts路径失败: {e}")
        return False, None

def test_file_operations():
    """测试文件操作功能"""
    print_separator("文件操作测试")

    try:
        import maya.cmds as cmds

        # 测试获取当前工作目录
        try:
            workspace = cmds.workspace(query=True, rootDirectory=True)
            print(f"✓ 当前工作目录: {workspace}")
        except Exception as e:
            print(f"✗ 获取工作目录失败: {e}")

        # 测试获取当前场景信息
        try:
            scene_name = cmds.file(query=True, sceneName=True)
            scene_modified = cmds.file(query=True, modified=True)

            if scene_name:
                print(f"✓ 当前场景文件: {os.path.basename(scene_name)}")
            else:
                print(f"✓ 当前场景: 未命名场景")

            print(f"✓ 场景修改状态: {'已修改' if scene_modified else '未修改'}")

        except Exception as e:
            print(f"✗ 获取场景信息失败: {e}")

        return True

    except ImportError:
        print("✗ 不在Maya环境中，跳过文件操作测试")
        return False

def test_system_info():
    """测试系统信息"""
    print_separator("系统信息")

    print(f"✓ 操作系统: {platform.system()}")
    print(f"✓ 系统版本: {platform.release()}")
    print(f"✓ Python版本: {sys.version}")
    print(f"✓ 当前工作目录: {os.getcwd()}")
    print(f"✓ 用户主目录: {os.path.expanduser('~')}")

def create_test_file(scripts_path):
    """在scripts目录中创建测试文件"""
    print_separator("创建测试文件")

    if not scripts_path or not os.path.exists(scripts_path):
        print("✗ Scripts路径无效，跳过测试文件创建")
        return False

    test_file_path = os.path.join(scripts_path, "maya_file_manager_test.py")

    try:
        test_content = '''# -*- coding: utf-8 -*-
"""
Maya文件管理器测试文件
这是一个由测试脚本自动创建的文件
创建时间: {timestamp}
"""

def test_function():
    """测试函数"""
    print("Maya文件管理器测试文件运行成功！")
    return True

if __name__ == "__main__":
    test_function()
'''.format(timestamp=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

        with open(test_file_path, 'w', encoding='utf-8') as f:
            f.write(test_content)

        print(f"✓ 测试文件创建成功: {test_file_path}")

        # 尝试导入测试文件
        try:
            sys.path.insert(0, scripts_path)
            import maya_file_manager_test
            maya_file_manager_test.test_function()
            print("✓ 测试文件导入和执行成功")
        except Exception as e:
            print(f"✗ 测试文件导入失败: {e}")

        # 清理测试文件
        try:
            os.remove(test_file_path)
            print("✓ 测试文件清理完成")
        except Exception as e:
            print(f"✗ 清理测试文件失败: {e}")

        return True

    except Exception as e:
        print(f"✗ 创建测试文件失败: {e}")
        return False

def run_comprehensive_test():
    """运行综合测试"""
    print_separator("Maya文件管理器核心功能测试", "=", 80)
    print(f"测试开始时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    test_results = []

    # 1. 系统信息测试
    test_system_info()
    print()

    # 2. Maya环境测试
    maya_available = test_maya_environment()
    test_results.append(("Maya环境", maya_available))
    print()

    # 3. Scripts路径测试
    path_success, scripts_path = test_scripts_path_function()
    test_results.append(("Scripts路径", path_success))
    print()

    # 4. 文件操作测试
    if maya_available:
        file_ops_success = test_file_operations()
        test_results.append(("文件操作", file_ops_success))
        print()

    # 5. 创建测试文件
    if path_success and scripts_path:
        test_file_success = create_test_file(scripts_path)
        test_results.append(("测试文件创建", test_file_success))
        print()

    # 测试结果汇总
    print_separator("测试结果汇总")

    passed_tests = 0
    total_tests = len(test_results)

    for test_name, result in test_results:
        status = "通过" if result else "失败"
        symbol = "✓" if result else "✗"
        print(f"{symbol} {test_name}: {status}")
        if result:
            passed_tests += 1

    print()
    print(f"测试完成: {passed_tests}/{total_tests} 项测试通过")

    if passed_tests == total_tests:
        print("🎉 所有测试都通过了！Maya文件管理器核心功能正常。")
    elif passed_tests > 0:
        print("⚠️  部分测试通过，请检查失败的项目。")
    else:
        print("❌ 所有测试都失败了，请检查Maya环境和配置。")

    print_separator("", "=", 80)
    print(f"测试结束时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def quick_test():
    """快速测试 - 只测试核心功能"""
    print_separator("快速测试模式")

    # 测试get_maya_scripts_path函数
    try:
        scripts_path, source = get_maya_scripts_path()
        print(f"✓ Scripts路径: {scripts_path}")
        print(f"✓ 路径来源: {source}")
        print(f"✓ 路径存在: {'是' if os.path.exists(scripts_path) else '否'}")

        # 测试Maya环境
        try:
            import maya.cmds as cmds
            maya_version = cmds.about(version=True)
            print(f"✓ Maya版本: {maya_version}")
            print("✓ 在Maya环境中运行")
        except ImportError:
            print("✓ 在非Maya环境中运行")

        print("✓ 快速测试完成 - 核心功能正常")

    except Exception as e:
        print(f"✗ 快速测试失败: {e}")

# 主执行部分
if __name__ == "__main__":
    # 这个脚本可以直接在Maya脚本编辑器中运行

    # 检查是否传入了参数来决定运行哪种测试
    if len(sys.argv) > 1 and sys.argv[1] == "quick":
        quick_test()
    else:
        run_comprehensive_test()

# 为了方便在Maya脚本编辑器中使用，提供一些快捷函数
def 运行完整测试():
    """中文函数名 - 运行完整测试"""
    run_comprehensive_test()

def 运行快速测试():
    """中文函数名 - 运行快速测试"""
    quick_test()

def 获取脚本路径():
    """中文函数名 - 获取Maya scripts路径"""
    scripts_path, source = get_maya_scripts_path()
    print(f"Scripts路径: {scripts_path}")
    print(f"路径来源: {source}")
    return scripts_path

# 使用说明
def 使用说明():
    """显示使用说明"""
    print_separator("使用说明")
    print("这个脚本提供了以下功能:")
    print()
    print("1. 完整测试:")
    print("   run_comprehensive_test()  # 或者 运行完整测试()")
    print()
    print("2. 快速测试:")
    print("   quick_test()  # 或者 运行快速测试()")
    print()
    print("3. 获取Scripts路径:")
    print("   get_maya_scripts_path()  # 或者 获取脚本路径()")
    print()
    print("4. 显示使用说明:")
    print("   使用说明()")
    print()
    print("直接运行脚本将执行完整测试。")
    print_separator("")

# 如果直接在Maya脚本编辑器中运行，显示使用说明
print("Maya文件管理器测试脚本已加载！")
print("输入 使用说明() 查看可用功能，或直接运行测试。")
print("=" * 50)
