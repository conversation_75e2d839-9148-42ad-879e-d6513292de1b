# -*- coding: utf-8 -*-
"""Blender插件 | 作者: MMQ"""

import os
from plugins.maya_plugin import PluginBase

class BlenderPlugin(PluginBase):
    """Blender插件"""
    
    def __init__(self):
        self.available = False
        self.bpy = None
        
        try:
            import bpy
            self.bpy = bpy
            self.available = True
        except ImportError:
            pass
    
    def get_name(self):
        """返回插件名称"""
        if self.available:
            try:
                version = self.bpy.app.version_string
                return f"Blender {version}"
            except:
                return "Blender"
        return "Blender (不可用)"
    
    def get_file_extension(self):
        """返回文件扩展名"""
        return ".blend"
    
    def open_file(self, file_path):
        """打开Blender文件"""
        if not self.available:
            return False
        
        try:
            self.bpy.ops.wm.open_mainfile(filepath=file_path)
            return True
        except Exception as e:
            print(f"打开Blender文件失败: {e}")
            return False
    
    def save_file(self, file_path):
        """保存Blender文件"""
        if not self.available:
            return False
        
        try:
            self.bpy.ops.wm.save_as_mainfile(filepath=file_path)
            return True
        except Exception as e:
            print(f"保存Blender文件失败: {e}")
            return False
    
    def check_unsaved_changes(self):
        """检查是否有未保存的更改"""
        if not self.available:
            return False
        
        try:
            return self.bpy.data.is_dirty
        except:
            return False
    
    def get_current_file_info(self):
        """获取当前文件信息"""
        if not self.available:
            return {
                'file': None,
                'modified': False,
                'exists': False
            }
        
        try:
            current_file = self.bpy.data.filepath
            modified = self.bpy.data.is_dirty
            
            return {
                'file': current_file,
                'modified': modified,
                'exists': bool(current_file and os.path.exists(current_file)),
                'name': os.path.basename(current_file) if current_file else "untitled.blend"
            }
        except Exception as e:
            print(f"获取Blender文件信息失败: {e}")
            return {
                'file': None,
                'modified': False,
                'exists': False
            }
    
    def new_scene(self):
        """新建场景"""
        if not self.available:
            return False
        
        try:
            self.bpy.ops.wm.read_homefile(use_empty=True)
            return True
        except Exception as e:
            print(f"新建Blender场景失败: {e}")
            return False
    
    def import_file(self, file_path):
        """导入文件"""
        if not self.available:
            return False
        
        try:
            ext = os.path.splitext(file_path)[1].lower()
            
            if ext == '.obj':
                self.bpy.ops.import_scene.obj(filepath=file_path)
            elif ext == '.fbx':
                self.bpy.ops.import_scene.fbx(filepath=file_path)
            elif ext == '.dae':
                self.bpy.ops.wm.collada_import(filepath=file_path)
            elif ext == '.blend':
                self.bpy.ops.wm.append(filepath=file_path)
            else:
                return False
            
            return True
        except Exception as e:
            print(f"导入Blender文件失败: {e}")
            return False
    
    def export_selection(self, file_path):
        """导出选中对象"""
        if not self.available:
            return False
        
        try:
            ext = os.path.splitext(file_path)[1].lower()
            
            if ext == '.obj':
                self.bpy.ops.export_scene.obj(filepath=file_path, use_selection=True)
            elif ext == '.fbx':
                self.bpy.ops.export_scene.fbx(filepath=file_path, use_selection=True)
            elif ext == '.dae':
                self.bpy.ops.wm.collada_export(filepath=file_path, selected=True)
            else:
                return False
            
            return True
        except Exception as e:
            print(f"导出Blender文件失败: {e}")
            return False
    
    def get_scene_info(self):
        """获取场景信息"""
        if not self.available:
            return {}
        
        try:
            scene = self.bpy.context.scene
            info = {
                'objects_count': len(self.bpy.data.objects),
                'selected_count': len(self.bpy.context.selected_objects),
                'frame_range': (scene.frame_start, scene.frame_end),
                'current_frame': scene.frame_current,
                'render_engine': scene.render.engine,
                'collections_count': len(self.bpy.data.collections)
            }
            return info
        except Exception as e:
            print(f"获取Blender场景信息失败: {e}")
            return {}
    
    def set_project(self, project_path):
        """设置Blender项目路径"""
        if not self.available:
            return False
        
        try:
            # Blender没有传统的项目概念，但可以设置输出路径
            self.bpy.context.scene.render.filepath = project_path
            return True
        except Exception as e:
            print(f"设置Blender项目失败: {e}")
            return False
    
    def get_project_path(self):
        """获取当前项目路径"""
        if not self.available:
            return None
        
        try:
            current_file = self.bpy.data.filepath
            if current_file:
                return os.path.dirname(current_file)
            return None
        except Exception as e:
            print(f"获取Blender项目路径失败: {e}")
            return None
    
    def is_available(self):
        """检查插件是否可用"""
        return self.available
    
    def get_supported_formats(self):
        """获取支持的文件格式"""
        return ['.blend']
    
    def validate_file(self, file_path):
        """验证文件是否有效"""
        if not os.path.exists(file_path):
            return False
        
        ext = os.path.splitext(file_path)[1].lower()
        return ext in self.get_supported_formats()
    
    def get_addon_info(self):
        """获取插件信息"""
        if not self.available:
            return {}
        
        try:
            addons = []
            for addon in self.bpy.context.preferences.addons:
                addons.append(addon.module)
            
            return {
                'enabled_addons': addons,
                'addon_count': len(addons)
            }
        except Exception as e:
            print(f"获取Blender插件信息失败: {e}")
            return {}
