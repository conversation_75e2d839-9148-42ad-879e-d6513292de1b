# -*- coding: utf-8 -*-
"""Blender插件 | 作者: MMQ"""

import os
from plugins.base_plugin import PluginBase

class BlenderPlugin(PluginBase):
    """Blender插件"""
    
    def __init__(self):
        self.available = False
        self.bpy = None
        
        try:
            import bpy
            self.bpy = bpy
            self.available = True
        except ImportError:
            pass
    
    def get_name(self):
        """返回插件名称"""
        if self.available:
            try:
                version = self.bpy.app.version_string
                return f"Blender {version}"
            except:
                return "Blender"
        return "Blender (不可用)"
    
    def get_file_extension(self):
        """返回文件扩展名"""
        return ".blend"
    
    def open_file(self, file_path):
        """打开Blender文件"""
        if not self.available:
            return False
        
        try:
            self.bpy.ops.wm.open_mainfile(filepath=file_path)
            return True
        except Exception as e:
            print(f"打开Blender文件失败: {e}")
            return False
    
    def save_file(self, file_path):
        """保存Blender文件"""
        if not self.available:
            return False
        
        try:
            self.bpy.ops.wm.save_as_mainfile(filepath=file_path)
            return True
        except Exception as e:
            print(f"保存Blender文件失败: {e}")
            return False
    
    def check_unsaved_changes(self):
        """检查是否有未保存的更改"""
        if not self.available:
            return False
        
        try:
            return self.bpy.data.is_dirty
        except:
            return False
    
    def get_current_file_info(self):
        """获取当前文件信息"""
        if not self.available:
            return {
                'file': None,
                'modified': False,
                'exists': False
            }
        
        try:
            current_file = self.bpy.data.filepath
            modified = self.bpy.data.is_dirty
            
            return {
                'file': current_file,
                'modified': modified,
                'exists': bool(current_file and os.path.exists(current_file)),
                'name': os.path.basename(current_file) if current_file else "untitled.blend"
            }
        except Exception as e:
            print(f"获取Blender文件信息失败: {e}")
            return {
                'file': None,
                'modified': False,
                'exists': False
            }
    

