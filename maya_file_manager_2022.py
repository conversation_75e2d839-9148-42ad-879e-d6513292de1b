# -*- coding: utf-8 -*-
"""
Maya文件管理器 - Maya 2022.5.1专用版本
作者: MMQ
版本: 1.0

专门为Maya 2022.5.1优化的文件管理器
直接复制粘贴到Maya脚本编辑器中运行！
"""

print("=" * 50)
print("Maya文件管理器 v1.0 - 启动中...")
print("=" * 50)

# 检查Maya环境
print("正在检查Maya环境...")
try:
    import maya.cmds as cmds
    import maya.mel as mel
    maya_version = cmds.about(version=True)
    print("Maya版本: " + str(maya_version))
    print("✅ Maya环境检查通过")
except Exception as e:
    print("❌ Maya环境检查失败: " + str(e))
    print("请确保在Maya中运行此脚本")

# 检查PySide2
print("正在检查PySide2...")
try:
    from PySide2.QtWidgets import *
    from PySide2.QtCore import *
    from PySide2.QtGui import *
    from maya.app.general.mayaMixin import MayaQWidgetDockableMixin
    print("✅ PySide2检查通过")
except Exception as e:
    print("❌ PySide2检查失败: " + str(e))
    print("请确保Maya版本支持PySide2")

import os
import datetime

class MayaFileManager(MayaQWidgetDockableMixin, QWidget):
    """Maya文件管理器主界面"""
    
    def __init__(self):
        super(MayaFileManager, self).__init__()
        self.setWindowTitle("Maya文件管理器 v1.0")
        self.setMinimumSize(450, 400)
        self.setObjectName("MayaFileManager2022")
        
        # 创建界面
        self.create_ui()
        
        # 记录启动信息
        self.log("文件管理器启动成功！")
        self.log("Maya版本: " + str(cmds.about(version=True)))
        
    def create_ui(self):
        """创建用户界面 - Maya原生风格"""
        layout = QVBoxLayout(self)
        layout.setSpacing(8)
        layout.setContentsMargins(8, 8, 8, 8)

        # Maya原生风格样式
        maya_style = """
            QWidget {
                background-color: #393939;
                color: #CCCCCC;
                font-family: "Segoe UI", Arial, sans-serif;
                font-size: 11px;
            }
            QLabel {
                color: #CCCCCC;
                background-color: transparent;
            }
            QPushButton {
                background-color: #4A4A4A;
                border: 1px solid #5A5A5A;
                color: #CCCCCC;
                padding: 6px 12px;
                border-radius: 3px;
                font-weight: normal;
            }
            QPushButton:hover {
                background-color: #5A5A5A;
                border-color: #6A6A6A;
            }
            QPushButton:pressed {
                background-color: #3A3A3A;
                border-color: #4A4A4A;
            }
            QComboBox {
                background-color: #4A4A4A;
                border: 1px solid #5A5A5A;
                color: #CCCCCC;
                padding: 4px 8px;
                border-radius: 3px;
            }
            QComboBox:hover {
                border-color: #6A6A6A;
            }
            QComboBox::drop-down {
                border: none;
                width: 16px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid #CCCCCC;
                margin-right: 4px;
            }
            QTextEdit {
                background-color: #2A2A2A;
                border: 1px solid #5A5A5A;
                color: #CCCCCC;
                padding: 4px;
                border-radius: 3px;
            }
        """
        self.setStyleSheet(maya_style)

        # 标题栏 - 简洁风格
        title = QLabel("文件管理器")
        title.setAlignment(Qt.AlignLeft)
        title.setStyleSheet("""
            QLabel {
                font-size: 12px;
                font-weight: bold;
                color: #FFFFFF;
                padding: 4px 0px;
                border-bottom: 1px solid #5A5A5A;
                margin-bottom: 8px;
            }
        """)
        layout.addWidget(title)

        # 打开文件按钮
        self.open_btn = QPushButton("打开Maya文件")
        self.open_btn.setMinimumHeight(28)
        self.open_btn.clicked.connect(self.open_file)
        layout.addWidget(self.open_btn)

        # 保存区域
        save_layout = QHBoxLayout()

        # 保存按钮
        self.save_btn = QPushButton("保存文件")
        self.save_btn.setMinimumHeight(28)
        self.save_btn.clicked.connect(self.save_file)
        save_layout.addWidget(self.save_btn, 2)

        # 版本号选择
        version_layout = QVBoxLayout()
        version_label = QLabel("版本号:")
        version_label.setStyleSheet("font-weight: bold; color: #CCCCCC; font-size: 11px;")
        version_layout.addWidget(version_label)

        self.version_combo = QComboBox()
        self.version_combo.setMinimumHeight(24)
        self.version_combo.currentTextChanged.connect(self.on_version_changed)
        version_layout.addWidget(self.version_combo)
        save_layout.addLayout(version_layout, 1)

        layout.addLayout(save_layout)
        
        # 文件信息
        self.file_info = QLabel("当前文件: 新建场景")
        self.file_info.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 10px;
                font-size: 12px;
            }
        """)
        self.file_info.setWordWrap(True)
        layout.addWidget(self.file_info)

        # 日志区域
        log_label = QLabel("操作日志:")
        log_label.setStyleSheet("font-weight: bold;")
        layout.addWidget(log_label)

        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(120)
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 8px;
                font-family: monospace;
                font-size: 10px;
            }
        """)
        layout.addWidget(self.log_text)
        
        # 初始化
        self.update_version_combo()
        self.update_file_info()
        
    def log(self, message):
        """记录日志"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = "[" + timestamp + "] " + message
        self.log_text.append(log_entry)
        print("[文件管理器] " + message)
        
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
        
    def update_file_info(self):
        """更新文件信息"""
        try:
            current_scene = cmds.file(query=True, sceneName=True)
            if current_scene:
                file_name = os.path.basename(current_scene)
                self.file_info.setText("当前文件: " + file_name + "\n路径: " + current_scene)
            else:
                self.file_info.setText("当前文件: 新建场景 (未保存)")
        except Exception as e:
            self.file_info.setText("获取文件信息失败: " + str(e))
            
    def get_project_save_dir(self):
        """获取项目保存目录"""
        return "D:/dev/Scgtest/0010/3d/mm/"
    
    def get_base_filename(self):
        """获取基础文件名"""
        return "dev_cgtest_0010_mm"
    
    def get_existing_versions(self):
        """获取已存在的版本号"""
        save_dir = self.get_project_save_dir()
        base_name = self.get_base_filename()
        versions = []
        
        if os.path.exists(save_dir):
            try:
                files = os.listdir(save_dir)
                for file in files:
                    if file.startswith(base_name) and file.endswith(('.ma', '.mb')):
                        parts = file.split('_')
                        if len(parts) >= 5 and parts[4].startswith('v'):
                            version_part = parts[4]
                            if version_part[1:].isdigit():
                                versions.append(version_part)
            except Exception as e:
                self.log("读取目录错误: " + str(e))
        
        versions = sorted(list(set(versions)))
        return versions
    
    def get_next_version(self, existing_versions):
        """获取下一个版本号"""
        if not existing_versions:
            return "v001"
        
        max_version = 0
        for version in existing_versions:
            try:
                version_num = int(version[1:])
                max_version = max(max_version, version_num)
            except ValueError:
                continue
        
        next_version = max_version + 1
        return "v" + str(next_version).zfill(3)
    
    def update_version_combo(self):
        """更新版本号下拉菜单"""
        try:
            self.version_combo.clear()
            
            existing_versions = self.get_existing_versions()
            next_version = self.get_next_version(existing_versions)
            
            for version in existing_versions:
                self.version_combo.addItem(version)
            
            self.version_combo.addItem(next_version)
            
            if self.version_combo.count() > 0:
                self.version_combo.setCurrentIndex(self.version_combo.count() - 1)
            
            self.log("版本号列表已更新，当前选择: " + self.version_combo.currentText())
            
        except Exception as e:
            self.log("更新版本号列表错误: " + str(e))
            self.version_combo.clear()
            self.version_combo.addItem("v001")
    
    def generate_filename(self):
        """生成完整文件名"""
        version = self.version_combo.currentText()
        base_name = self.get_base_filename()
        filename = base_name + "_" + version + "_mmq.ma"
        return filename

    def on_version_changed(self, version):
        """版本号选择改变时的回调"""
        if version:
            self.log("版本号已切换到: " + version)
            # 检查当前场景是否需要另存为新版本
            current_scene = cmds.file(query=True, sceneName=True)
            if current_scene and self.get_base_filename() in os.path.basename(current_scene):
                # 如果当前是项目文件，询问是否另存为新版本
                reply = QMessageBox.question(
                    self, "版本切换",
                    "是否将当前场景另存为版本 " + version + "？",
                    QMessageBox.Yes | QMessageBox.No
                )
                if reply == QMessageBox.Yes:
                    self.save_as_version(version)

    def save_as_version(self, version):
        """另存为指定版本"""
        try:
            save_dir = self.get_project_save_dir()

            if not os.path.exists(save_dir):
                try:
                    os.makedirs(save_dir)
                    self.log("已创建目录: " + save_dir)
                except Exception as e:
                    self.log("无法创建目录 " + save_dir + ": " + str(e))
                    QMessageBox.critical(self, "错误", "无法创建保存目录: " + str(e))
                    return

            # 生成指定版本的文件名
            base_name = self.get_base_filename()
            filename = base_name + "_" + version + "_mmq.ma"
            file_path = os.path.join(save_dir, filename)

            self.log("正在另存为版本: " + filename)

            cmds.file(rename=file_path)
            cmds.file(save=True, type="mayaAscii")

            self.log("文件另存为成功: " + filename)
            self.update_file_info()
            self.update_version_combo()

            QMessageBox.information(self, "另存为成功", "文件已另存为:\n" + filename)

        except Exception as e:
            error_msg = "另存为版本时发生错误: " + str(e)
            self.log(error_msg)
            QMessageBox.critical(self, "另存为错误", error_msg)

    def open_file(self):
        """打开文件"""
        try:
            self.log("正在打开文件选择对话框...")

            # 使用项目保存目录作为默认打开路径
            default_open_dir = self.get_project_save_dir()
            if not os.path.exists(default_open_dir):
                try:
                    workspace = cmds.workspace(query=True, rootDirectory=True)
                except:
                    workspace = os.path.expanduser("~")
                default_open_dir = workspace

            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择Maya文件",
                default_open_dir,
                "Maya Files (*.ma *.mb);;All Files (*.*)"
            )

            if file_path:
                if cmds.file(query=True, modified=True):
                    reply = QMessageBox.question(
                        self, "确认",
                        "当前场景有未保存的更改。是否继续？",
                        QMessageBox.Yes | QMessageBox.No
                    )
                    if reply == QMessageBox.No:
                        self.log("用户取消了文件打开")
                        return

                cmds.file(file_path, open=True, force=True)
                self.log("成功打开文件: " + os.path.basename(file_path))
                self.update_file_info()
                QMessageBox.information(self, "成功", "文件已打开:\n" + os.path.basename(file_path))

            else:
                self.log("用户取消了文件选择")

        except Exception as e:
            error_msg = "打开文件时发生错误: " + str(e)
            self.log(error_msg)
            QMessageBox.critical(self, "错误", error_msg)

    def save_file(self):
        """保存文件"""
        try:
            current_scene = cmds.file(query=True, sceneName=True)

            if current_scene and self.get_base_filename() in os.path.basename(current_scene):
                cmds.file(save=True)
                self.log("文件保存成功: " + os.path.basename(current_scene))
                self.update_file_info()
                QMessageBox.information(self, "成功", "文件保存成功！")
            else:
                self.save_with_version()

        except Exception as e:
            error_msg = "保存文件时发生错误: " + str(e)
            self.log(error_msg)
            QMessageBox.critical(self, "错误", error_msg)

    def save_with_version(self):
        """使用版本号保存文件"""
        try:
            save_dir = self.get_project_save_dir()

            if not os.path.exists(save_dir):
                try:
                    os.makedirs(save_dir)
                    self.log("已创建目录: " + save_dir)
                except Exception as e:
                    self.log("无法创建目录 " + save_dir + ": " + str(e))
                    QMessageBox.critical(self, "错误", "无法创建保存目录: " + str(e))
                    return

            filename = self.generate_filename()
            file_path = os.path.join(save_dir, filename)

            self.log("正在保存文件: " + filename)

            cmds.file(rename=file_path)
            cmds.file(save=True, type="mayaAscii")

            self.log("文件保存成功: " + filename)
            self.update_file_info()
            self.update_version_combo()

            QMessageBox.information(self, "保存成功", "文件已保存:\n" + filename)

        except Exception as e:
            error_msg = "保存文件时发生错误: " + str(e)
            self.log(error_msg)
            QMessageBox.critical(self, "保存错误", error_msg)


# 全局变量
file_manager_window = None

def close_existing_windows():
    """关闭已存在的文件管理器窗口"""
    try:
        # 删除可能存在的工作区控件
        workspace_control_name = "MayaFileManager2022WorkspaceControl"
        if cmds.workspaceControl(workspace_control_name, exists=True):
            cmds.deleteUI(workspace_control_name)
            print("已删除现有的工作区控件")

        # 删除可能存在的窗口
        window_name = "MayaFileManager2022"
        if cmds.window(window_name, exists=True):
            cmds.deleteUI(window_name)
            print("已删除现有的窗口")

    except Exception as e:
        print("清理现有窗口时发生错误: " + str(e))

def show_file_manager():
    """显示文件管理器"""
    global file_manager_window

    try:
        # 先关闭已存在的窗口
        close_existing_windows()

        # 如果全局变量中有窗口实例，先清理
        if file_manager_window is not None:
            try:
                file_manager_window.close()
                file_manager_window.deleteLater()
            except:
                pass
            file_manager_window = None

        # 创建新的文件管理器窗口
        file_manager_window = MayaFileManager()
        file_manager_window.show(dockable=True)

        print("✅ Maya文件管理器界面已成功启动！")
        return file_manager_window

    except Exception as e:
        print("❌ 启动文件管理器时发生错误: " + str(e))
        print("尝试运行: close_existing_windows() 然后重新启动")
        return None

# 立即启动
print("正在启动文件管理器界面...")
try:
    window = show_file_manager()
    if window:
        print("🎉 启动成功！文件管理器界面已显示。")
        print("如需重新打开，请运行: show_file_manager()")
    else:
        print("❌ 启动失败")
except Exception as e:
    print("❌ 启动异常: " + str(e))
    print("请确保在Maya 2022.5.1中运行此脚本")

# 提供手动清理和重启的函数
def restart_file_manager():
    """重启文件管理器"""
    print("🔄 正在重启文件管理器...")
    close_existing_windows()
    return show_file_manager()

# 中文函数名
def 重启文件管理器():
    """重启文件管理器 - 中文版"""
    return restart_file_manager()

def 关闭现有窗口():
    """关闭现有窗口 - 中文版"""
    return close_existing_windows()

print("=" * 50)
print("如果遇到窗口名称冲突问题，请运行:")
print("restart_file_manager() 或 重启文件管理器()")
print("=" * 50)
