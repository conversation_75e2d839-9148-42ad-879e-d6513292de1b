# -*- coding: utf-8 -*-
"""数据模型 | 作者: MMQ"""

import os
from utils import *

class FileManagerModel:
    """文件管理器数据模型"""

    def __init__(self, plugin, save_dir, base_name):
        self.plugin = plugin
        self.save_dir = os.path.normpath(save_dir)
        self.base_name = base_name
        self.current_version = None

        ensure_directory(self.save_dir)
    
    def get_file_extension(self):
        """获取文件扩展名"""
        return self.plugin.get_file_extension() if self.plugin else ".txt"

    def get_filename(self, version):
        """根据版本号生成文件名"""
        ext = self.get_file_extension()
        return f"{self.base_name}_{version}{ext}"

    def get_file_path(self, version):
        """根据版本号生成完整文件路径"""
        filename = self.get_filename(version)
        return os.path.join(self.save_dir, filename)

    def scan_existing_versions(self):
        """扫描已存在的版本"""
        ext = self.get_file_extension()
        return scan_versions(self.save_dir, self.base_name, ext)

    def generate_version_list(self, future_count=5):
        """生成版本列表"""
        existing_versions = self.scan_existing_versions()

        if not existing_versions:
            return [format_version(i) for i in range(future_count, 0, -1)]

        max_version = max([parse_version(v) for v in existing_versions])

        future_versions = []
        for i in range(1, future_count + 1):
            future_versions.append(format_version(max_version + i))

        all_versions = existing_versions + future_versions
        return sort_versions(all_versions, reverse=True)

    def get_latest_version(self):
        """获取最新版本"""
        existing_versions = self.scan_existing_versions()
        return get_latest_version(existing_versions)

    def get_next_version(self):
        """获取下一个版本号"""
        existing_versions = self.scan_existing_versions()
        return get_next_version(existing_versions)
    
    def get_file_info(self, version):
        """获取文件信息"""
        file_path = self.get_file_path(version)
        info = get_file_info(file_path)
        info['version'] = version
        info['path'] = file_path

        existing_versions = self.scan_existing_versions()
        latest_version = get_latest_version(existing_versions)

        info['color'] = get_version_color(version, latest_version, existing_versions)
        info['icon'] = get_status_icon(version, latest_version, existing_versions)

        if info['exists']:
            if version == latest_version:
                info['status'] = 'latest'
                info['status_text'] = '最新版本'
            else:
                info['status'] = 'history'
                info['status_text'] = '历史版本'
        else:
            info['status'] = 'new'
            info['status_text'] = '新版本'

        return info

    def file_exists(self, version):
        """检查文件是否存在"""
        file_path = self.get_file_path(version)
        return os.path.exists(file_path)

    def get_default_version(self):
        """获取默认版本"""
        latest = self.get_latest_version()
        return latest if latest else "v001"

    def set_current_version(self, version):
        """设置当前版本"""
        self.current_version = version

    def get_current_version(self):
        """获取当前版本"""
        return self.current_version

    def refresh(self):
        """刷新数据"""
        pass
