# -*- coding: utf-8 -*-
"""数据模型 - MVC架构的Model层 | 作者: MMQ"""

import os
from typing import List, Dict, Optional
from utils import VersionCalculator, FileUtils, PathUtils

class FileManagerModel:
    """文件管理器数据模型"""
    
    def __init__(self, plugin, save_dir: str, base_name: str):
        self.plugin = plugin
        self.save_dir = PathUtils.normalize_path(save_dir)
        self.base_name = base_name
        self.current_version = None
        self.versions_cache = []
        self.file_info_cache = {}
        
        # 确保目录存在
        PathUtils.ensure_directory(self.save_dir)
    
    def get_file_extension(self) -> str:
        """获取文件扩展名"""
        return self.plugin.get_file_extension() if self.plugin else ".txt"
    
    def get_filename(self, version: str) -> str:
        """根据版本号生成文件名"""
        ext = self.get_file_extension()
        return f"{self.base_name}_{version}{ext}"
    
    def get_file_path(self, version: str) -> str:
        """根据版本号生成完整文件路径"""
        filename = self.get_filename(version)
        return PathUtils.join_path(self.save_dir, filename)
    
    def scan_existing_versions(self) -> List[str]:
        """扫描已存在的版本"""
        ext = self.get_file_extension()
        versions = FileUtils.scan_versions(self.save_dir, self.base_name, ext)
        self.versions_cache = versions
        return versions
    
    def generate_version_list(self, future_count: int = 5) -> List[str]:
        """生成版本列表（包含现有版本和未来版本）"""
        existing_versions = self.scan_existing_versions()
        
        if not existing_versions:
            # 如果没有现有版本，从v001开始
            return [VersionCalculator.format_version(i) for i in range(future_count, 0, -1)]
        
        # 获取最大版本号
        max_version = max([VersionCalculator.parse_version(v) for v in existing_versions])
        
        # 生成未来版本
        future_versions = []
        for i in range(1, future_count + 1):
            future_versions.append(VersionCalculator.format_version(max_version + i))
        
        # 合并现有版本和未来版本，按降序排列
        all_versions = existing_versions + future_versions
        return VersionCalculator.sort_versions(all_versions, reverse=True)
    
    def get_latest_version(self) -> Optional[str]:
        """获取最新版本"""
        existing_versions = self.scan_existing_versions()
        return VersionCalculator.get_latest_version(existing_versions)
    
    def get_next_version(self) -> str:
        """获取下一个版本号"""
        existing_versions = self.scan_existing_versions()
        return VersionCalculator.get_next_version(existing_versions)
    
    def get_file_info(self, version: str) -> Dict:
        """获取文件信息"""
        file_path = self.get_file_path(version)
        
        # 检查缓存
        if file_path in self.file_info_cache:
            cached_info = self.file_info_cache[file_path]
            # 检查文件是否被修改
            if os.path.exists(file_path):
                current_mtime = os.path.getmtime(file_path)
                if cached_info.get('mtime') == current_mtime:
                    return cached_info
        
        # 获取文件信息
        info = FileUtils.get_file_info(file_path)
        info['version'] = version
        info['path'] = file_path
        
        # 添加版本状态
        existing_versions = self.versions_cache or self.scan_existing_versions()
        latest_version = VersionCalculator.get_latest_version(existing_versions)
        
        if info['exists']:
            if version == latest_version:
                info['status'] = 'latest'
                info['status_text'] = '最新版本'
                info['color'] = (46, 204, 113)  # 绿色
                info['icon'] = '🟢'
            else:
                info['status'] = 'history'
                info['status_text'] = '历史版本'
                info['color'] = (231, 76, 60)   # 红色
                info['icon'] = '🔴'
        else:
            info['status'] = 'new'
            info['status_text'] = '新版本'
            info['color'] = (255, 255, 255)     # 白色
            info['icon'] = '⚪'
        
        # 缓存信息
        if os.path.exists(file_path):
            info['mtime'] = os.path.getmtime(file_path)
        self.file_info_cache[file_path] = info
        
        return info
    
    def file_exists(self, version: str) -> bool:
        """检查文件是否存在"""
        file_path = self.get_file_path(version)
        return os.path.exists(file_path)
    
    def get_default_version(self) -> str:
        """获取默认版本"""
        latest = self.get_latest_version()
        return latest if latest else "v001"
    
    def validate_version(self, version: str) -> bool:
        """验证版本号格式"""
        from utils import ValidationUtils
        return ValidationUtils.is_valid_version(version)
    
    def get_project_info(self) -> Dict:
        """获取项目信息"""
        existing_versions = self.scan_existing_versions()
        latest_version = self.get_latest_version()
        
        info = {
            'save_dir': self.save_dir,
            'base_name': self.base_name,
            'extension': self.get_file_extension(),
            'total_versions': len(existing_versions),
            'latest_version': latest_version,
            'next_version': self.get_next_version(),
            'plugin_name': self.plugin.get_name() if self.plugin else 'Unknown'
        }
        
        # 计算总文件大小
        total_size = 0
        for version in existing_versions:
            file_path = self.get_file_path(version)
            total_size += FileUtils.get_file_size(file_path)
        info['total_size_mb'] = total_size
        
        return info
    
    def clear_cache(self):
        """清除缓存"""
        self.versions_cache.clear()
        self.file_info_cache.clear()
    
    def refresh(self):
        """刷新数据"""
        self.clear_cache()
        self.scan_existing_versions()
    
    def set_current_version(self, version: str):
        """设置当前版本"""
        if self.validate_version(version):
            self.current_version = version
    
    def get_current_version(self) -> Optional[str]:
        """获取当前版本"""
        return self.current_version
    
    def get_version_history(self) -> List[Dict]:
        """获取版本历史"""
        existing_versions = self.scan_existing_versions()
        history = []
        
        for version in existing_versions:
            info = self.get_file_info(version)
            history.append({
                'version': version,
                'size': info['size'],
                'modified': info['modified'],
                'status': info['status']
            })
        
        return history
    
    def update_save_dir(self, new_dir: str):
        """更新保存目录"""
        new_dir = PathUtils.normalize_path(new_dir)
        if new_dir != self.save_dir:
            self.save_dir = new_dir
            PathUtils.ensure_directory(self.save_dir)
            self.refresh()
    
    def update_base_name(self, new_name: str):
        """更新基础名称"""
        from utils import ValidationUtils
        clean_name = ValidationUtils.sanitize_filename(new_name)
        if clean_name != self.base_name:
            self.base_name = clean_name
            self.refresh()
