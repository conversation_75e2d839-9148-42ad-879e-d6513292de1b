# -*- coding: utf-8 -*-
"""
Maya文件管理器 - 最终优化版本
作者: MMQ | 适用于: Maya 2022.5.1
功能完整，代码精简，UI优化
"""

print("🚀 正在启动Maya文件管理器...")

# 导入模块
try:
    import maya.cmds as cmds, maya.mel as mel, os, datetime
    from maya.app.general.mayaMixin import MayaQWidgetDockableMixin
    from PySide2.QtWidgets import *
    from PySide2.QtCore import *
    from PySide2.QtGui import *
    print("✅ 模块导入成功")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")

class MayaFileManager(MayaQWidgetDockableMixin, QWidget):
    """Maya文件管理器主界面"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Maya文件管理器 v1.0")
        self.setMinimumSize(450, 400)
        self.setObjectName("MayaFileManagerFinal")
        
        # 项目配置
        self.save_dir = "D:/dev/Scgtest/0010/3d/mm/"
        self.base_name = "dev_cgtest_0010_mm"
        self.log_expanded = True
        
        self.create_ui()
        self.update_versions()
        self.update_file_info()
        self.log(f"文件管理器启动成功！Maya版本: {cmds.about(version=True)}")
        
    def create_ui(self):
        """创建用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(8)
        layout.setContentsMargins(8, 8, 8, 8)
        
        # Maya原生样式 - 优化版
        self.setStyleSheet("""
            QWidget { 
                background-color: #393939; color: #CCCCCC; 
                font-family: "Segoe UI"; font-size: 11px; 
            }
            QPushButton { 
                background-color: #4A4A4A; border: 1px solid #5A5A5A; 
                color: #CCCCCC; padding: 6px 12px; border-radius: 3px; 
            }
            QPushButton:hover { background-color: #5A5A5A; border-color: #6A6A6A; }
            QPushButton:pressed { background-color: #3A3A3A; }
            QComboBox { 
                background-color: #4A4A4A; border: 1px solid #5A5A5A; 
                color: #CCCCCC; padding: 4px 8px; border-radius: 2px;
                selection-background-color: #5A5A5A;
            }
            QComboBox::drop-down { 
                border: none; width: 16px; 
                background-color: #4A4A4A;
            }
            QComboBox::down-arrow { 
                image: none; width: 0; height: 0;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid #CCCCCC;
                margin-right: 4px;
            }
            QComboBox QAbstractItemView {
                background-color: #4A4A4A; 
                border: 1px solid #5A5A5A;
                selection-background-color: #5A5A5A;
                outline: none;
            }
            QTextEdit { 
                background-color: #2A2A2A; border: 1px solid #5A5A5A; 
                color: #CCCCCC; padding: 4px; border-radius: 3px; 
            }
            QScrollBar:vertical {
                background-color: #2A2A2A; width: 12px; border: none;
            }
            QScrollBar::handle:vertical {
                background-color: #5A5A5A; border-radius: 6px; min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #6A6A6A;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background-color: transparent;
            }
            QLabel { color: #CCCCCC; background-color: transparent; }
        """)
        
        # 标题
        title = QLabel("文件管理器")
        title.setStyleSheet("font-size: 12px; font-weight: bold; color: #FFFFFF; padding: 4px 0px; border-bottom: 1px solid #5A5A5A; margin-bottom: 8px;")
        layout.addWidget(title)
        
        # 创建按钮和版本选择区域
        for btn_text, combo_label, combo_attr, callback in [
            ("打开Maya文件", "打开版本:", "open_version_combo", self.open_file),
            ("保存文件", "版本号:", "version_combo", self.save_file)
        ]:
            h_layout = QHBoxLayout()
            btn = QPushButton(btn_text)
            btn.setMinimumHeight(28)
            btn.clicked.connect(callback)
            h_layout.addWidget(btn, 2)
            
            v_layout = QVBoxLayout()
            v_layout.addWidget(QLabel(combo_label))
            combo = QComboBox()
            combo.setMinimumHeight(24)
            setattr(self, combo_attr, combo)
            v_layout.addWidget(combo)
            h_layout.addLayout(v_layout, 1)
            layout.addLayout(h_layout)
        
        # 版本切换日志
        self.version_combo.currentTextChanged.connect(lambda v: self.log(f"版本号已切换到: {v}") if v else None)
        
        # 文件信息
        self.file_info = QLabel("当前文件: 新建场景")
        self.file_info.setStyleSheet("background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 6px; padding: 10px; font-size: 12px;")
        self.file_info.setWordWrap(True)
        layout.addWidget(self.file_info)
        
        # 可折叠日志区域
        log_header = QHBoxLayout()
        self.log_toggle_btn = QPushButton("▼ 操作日志")
        self.log_toggle_btn.setStyleSheet("text-align: left; padding: 4px 8px; font-weight: bold; border: none; background-color: transparent;")
        self.log_toggle_btn.clicked.connect(self.toggle_log)
        log_header.addWidget(self.log_toggle_btn)
        log_header.addStretch()
        layout.addLayout(log_header)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(120)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
    def toggle_log(self):
        """切换日志显示"""
        self.log_expanded = not self.log_expanded
        self.log_text.setVisible(self.log_expanded)
        self.log_toggle_btn.setText("▼ 操作日志" if self.log_expanded else "▶ 操作日志")
        
    def log(self, message):
        """记录日志"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        print(f"[文件管理器] {message}")
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
        
    def update_file_info(self):
        """更新文件信息"""
        try:
            current_scene = cmds.file(query=True, sceneName=True)
            if current_scene:
                file_name = os.path.basename(current_scene)
                self.file_info.setText(f"当前文件: {file_name}\n路径: {current_scene}")
            else:
                self.file_info.setText("当前文件: 新建场景 (未保存)")
        except Exception as e:
            self.file_info.setText(f"获取文件信息失败: {str(e)}")
            
    def get_existing_versions(self):
        """获取已存在的版本号"""
        versions = []
        if os.path.exists(self.save_dir):
            try:
                for file in os.listdir(self.save_dir):
                    if file.startswith(self.base_name) and file.endswith(('.ma', '.mb')):
                        parts = file.split('_')
                        if len(parts) >= 5 and parts[4].startswith('v') and parts[4][1:].isdigit():
                            versions.append(parts[4])
            except Exception as e:
                self.log(f"读取目录错误: {str(e)}")
        return sorted(list(set(versions)))
    
    def get_next_version(self, existing_versions):
        """获取下一个版本号"""
        if not existing_versions:
            return "v001"
        max_version = max([int(v[1:]) for v in existing_versions if v[1:].isdigit()] or [0])
        return f"v{max_version + 1:03d}"
    
    def update_versions(self):
        """更新版本号下拉菜单"""
        try:
            existing_versions = self.get_existing_versions()
            next_version = self.get_next_version(existing_versions)
            
            # 更新保存版本下拉菜单
            self.version_combo.clear()
            for version in existing_versions + [next_version]:
                self.version_combo.addItem(version)
            if self.version_combo.count() > 0:
                self.version_combo.setCurrentIndex(self.version_combo.count() - 1)
            
            # 更新打开版本下拉菜单
            self.open_version_combo.clear()
            if existing_versions:
                for version in existing_versions:
                    self.open_version_combo.addItem(version)
                self.open_version_combo.setCurrentIndex(self.open_version_combo.count() - 1)
            else:
                self.open_version_combo.addItem("v001")
                
            self.log(f"版本列表已更新，保存版本: {self.version_combo.currentText()}, 打开版本: {self.open_version_combo.currentText()}")
            
        except Exception as e:
            self.log(f"更新版本列表错误: {str(e)}")
            for combo in [self.version_combo, self.open_version_combo]:
                combo.clear()
                combo.addItem("v001")
    
    def generate_filename(self, version=None):
        """生成文件名"""
        version = version or self.version_combo.currentText()
        return f"{self.base_name}_{version}_mmq.ma"
    
    def extract_version_from_filename(self, filename):
        """从文件名提取版本号"""
        try:
            for part in filename.split('_'):
                if part.startswith('v') and part[1:].isdigit():
                    return part
        except:
            pass
        return None
    
    def check_file_exists_and_confirm(self, file_path, version):
        """检查文件是否存在并确认覆盖"""
        if os.path.exists(file_path):
            reply = QMessageBox.question(
                self, "文件已存在",
                f"版本 {version} 已存在，是否覆盖此文件？\n\n文件: {os.path.basename(file_path)}",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.No:
                self.log("用户取消了文件覆盖")
                return False
        return True

    def open_file(self):
        """打开指定版本的文件"""
        try:
            selected_version = self.open_version_combo.currentText()
            filename = self.generate_filename(selected_version)
            file_path = os.path.join(self.save_dir, filename)

            if not os.path.exists(file_path):
                QMessageBox.warning(self, "文件不存在", f"版本 {selected_version} 的文件不存在:\n{filename}")
                return

            if cmds.file(query=True, modified=True):
                if QMessageBox.question(self, "确认", "当前场景有未保存的更改。是否继续？", QMessageBox.Yes | QMessageBox.No) == QMessageBox.No:
                    self.log("用户取消了文件打开")
                    return

            cmds.file(file_path, open=True, force=True)
            self.log(f"成功打开版本 {selected_version}: {filename}")
            self.update_file_info()
            QMessageBox.information(self, "成功", f"已打开版本 {selected_version}:\n{filename}")

        except Exception as e:
            error_msg = f"打开文件时发生错误: {str(e)}"
            self.log(error_msg)
            QMessageBox.critical(self, "错误", error_msg)

    def save_file(self):
        """保存文件"""
        try:
            current_scene = cmds.file(query=True, sceneName=True)
            selected_version = self.version_combo.currentText()

            # 检查当前场景是否是项目文件且版本相同
            if (current_scene and self.base_name in os.path.basename(current_scene) and
                self.extract_version_from_filename(os.path.basename(current_scene)) == selected_version):
                # 版本相同，检查覆盖确认
                if not self.check_file_exists_and_confirm(current_scene, selected_version):
                    return
                cmds.file(save=True)
                self.log(f"文件保存成功: {os.path.basename(current_scene)}")
                self.update_file_info()
                QMessageBox.information(self, "成功", "文件保存成功！")
                return

            # 另存为新版本
            self.save_as_version(selected_version)

        except Exception as e:
            error_msg = f"保存文件时发生错误: {str(e)}"
            self.log(error_msg)
            QMessageBox.critical(self, "错误", error_msg)

    def save_as_version(self, version):
        """另存为指定版本"""
        try:
            # 创建目录
            if not os.path.exists(self.save_dir):
                try:
                    os.makedirs(self.save_dir)
                    self.log(f"已创建目录: {self.save_dir}")
                except Exception as e:
                    self.log(f"无法创建目录: {str(e)}")
                    QMessageBox.critical(self, "错误", f"无法创建保存目录: {str(e)}")
                    return

            filename = self.generate_filename(version)
            file_path = os.path.join(self.save_dir, filename)

            # 检查覆盖确认
            if not self.check_file_exists_and_confirm(file_path, version):
                return

            self.log(f"正在保存为版本: {filename}")
            cmds.file(rename=file_path)
            cmds.file(save=True, type="mayaAscii")

            self.log(f"文件保存成功: {filename}")
            self.update_file_info()
            self.update_versions()
            QMessageBox.information(self, "保存成功", f"文件已保存为:\n{filename}")

        except Exception as e:
            error_msg = f"保存版本时发生错误: {str(e)}"
            self.log(error_msg)
            QMessageBox.critical(self, "保存错误", error_msg)


# 全局变量和启动函数
file_manager_window = None

def close_existing_windows():
    """关闭已存在的窗口"""
    try:
        for name in ["MayaFileManagerFinalWorkspaceControl", "MayaFileManagerFinal"]:
            if (cmds.workspaceControl(name, exists=True) if "Workspace" in name else cmds.window(name, exists=True)):
                cmds.deleteUI(name)
                print(f"已删除现有的{'工作区控件' if 'Workspace' in name else '窗口'}")
    except Exception as e:
        print(f"清理现有窗口时发生错误: {str(e)}")

def show_file_manager():
    """显示文件管理器"""
    global file_manager_window
    try:
        close_existing_windows()
        if file_manager_window:
            try:
                file_manager_window.close()
                file_manager_window.deleteLater()
            except:
                pass
            file_manager_window = None

        file_manager_window = MayaFileManager()
        file_manager_window.show(dockable=True)
        print("✅ Maya文件管理器界面已成功启动！")
        return file_manager_window
    except Exception as e:
        print(f"❌ 启动文件管理器时发生错误: {str(e)}")
        return None

# 中文函数名和启动
重启文件管理器 = lambda: (close_existing_windows(), show_file_manager())[1]
关闭现有窗口 = close_existing_windows

# 立即启动
print("正在启动文件管理器界面...")
try:
    window = show_file_manager()
    print("🎉 启动成功！文件管理器界面已显示。" if window else "❌ 启动失败")
except Exception as e:
    print(f"❌ 启动异常: {str(e)}")

print("=" * 50)
print("如需重启: show_file_manager() 或 重启文件管理器()")
print("=" * 50)
