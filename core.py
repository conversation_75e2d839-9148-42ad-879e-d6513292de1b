# -*- coding: utf-8 -*-
"""核心功能模块 | 作者: MMQ"""

import os
import sys

def get_current_software():
    """检测当前软件"""
    if 'maya.cmds' in sys.modules:
        return 'maya'
    elif 'bpy' in sys.modules:
        return 'blender'
    return 'standalone'

def get_project_path():
    """获取项目路径"""
    software = get_current_software()

    if software == 'maya':
        try:
            import maya.cmds as cmds
            current_file = cmds.file(query=True, sceneName=True)
            if current_file:
                return os.path.dirname(current_file)
            return "D:/dev/Scgtest/0010/3d/mm/"
        except:
            pass

    elif software == 'blender':
        try:
            import bpy
            current_file = bpy.data.filepath
            if current_file:
                return os.path.dirname(current_file)
        except:
            pass

    return os.path.expanduser("~/Documents/Projects")


