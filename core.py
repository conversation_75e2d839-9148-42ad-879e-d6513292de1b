# -*- coding: utf-8 -*-
"""核心功能模块 - 软件检测和路径管理 | 作者: MMQ"""

import os
import sys
import platform

def get_current_software():
    """检测当前运行的软件环境"""
    # 检查已导入的模块
    if 'maya.cmds' in sys.modules or 'maya' in sys.modules:
        return 'maya'
    elif 'bpy' in sys.modules:
        return 'blender'
    elif 'pymxs' in sys.modules:
        return 'max'
    elif 'hou' in sys.modules:
        return 'houdini'
    elif 'c4d' in sys.modules:
        return 'cinema4d'
    
    # 检查进程名称
    try:
        import psutil
        process_name = psutil.Process().name().lower()
        if 'maya' in process_name:
            return 'maya'
        elif 'blender' in process_name:
            return 'blender'
        elif '3dsmax' in process_name or 'max' in process_name:
            return 'max'
        elif 'houdini' in process_name:
            return 'houdini'
        elif 'cinema4d' in process_name or 'c4d' in process_name:
            return 'cinema4d'
    except:
        pass
    
    # 检查环境变量
    if os.environ.get('MAYA_LOCATION'):
        return 'maya'
    elif os.environ.get('BLENDER_USER_SCRIPTS'):
        return 'blender'
    
    # 默认返回独立模式
    return 'standalone'

def get_project_path():
    """获取当前项目路径"""
    software = get_current_software()
    
    if software == 'maya':
        return get_maya_project_path()
    elif software == 'blender':
        return get_blender_project_path()
    elif software == 'max':
        return get_max_project_path()
    elif software == 'houdini':
        return get_houdini_project_path()
    else:
        return get_default_project_path()

def get_maya_project_path():
    """获取Maya项目路径"""
    try:
        import maya.cmds as cmds
        
        # 尝试获取当前场景文件路径
        current_file = cmds.file(query=True, sceneName=True)
        if current_file:
            return os.path.dirname(current_file)
        
        # 获取Maya工作区路径
        workspace = cmds.workspace(query=True, rootDirectory=True)
        if workspace:
            return workspace
        
        # 默认Maya项目路径
        return "D:/dev/Scgtest/0010/3d/mm/"
        
    except:
        return get_default_project_path()

def get_blender_project_path():
    """获取Blender项目路径"""
    try:
        import bpy
        
        # 获取当前文件路径
        current_file = bpy.data.filepath
        if current_file:
            return os.path.dirname(current_file)
        
        # 默认Blender项目路径
        return os.path.expanduser("~/Documents/Blender_Projects")
        
    except:
        return get_default_project_path()

def get_max_project_path():
    """获取3ds Max项目路径"""
    try:
        import pymxs
        rt = pymxs.runtime
        
        # 获取当前文件路径
        current_file = rt.maxFilePath + rt.maxFileName
        if current_file and current_file != "":
            return rt.maxFilePath
        
        # 获取项目路径
        project_path = rt.pathConfig.getCurrentProjectFolder()
        if project_path:
            return project_path
        
        # 默认Max项目路径
        return os.path.expanduser("~/Documents/3dsMax_Projects")
        
    except:
        return get_default_project_path()

def get_houdini_project_path():
    """获取Houdini项目路径"""
    try:
        import hou
        
        # 获取当前文件路径
        current_file = hou.hipFile.path()
        if current_file and current_file != "untitled.hip":
            return os.path.dirname(current_file)
        
        # 获取项目路径
        project_path = hou.getenv("HIP")
        if project_path:
            return project_path
        
        # 默认Houdini项目路径
        return os.path.expanduser("~/Documents/Houdini_Projects")
        
    except:
        return get_default_project_path()

def get_default_project_path():
    """获取默认项目路径"""
    # 根据操作系统返回合适的默认路径
    if platform.system() == "Windows":
        return os.path.expanduser("~/Documents/Projects")
    elif platform.system() == "Darwin":  # macOS
        return os.path.expanduser("~/Documents/Projects")
    else:  # Linux
        return os.path.expanduser("~/Projects")

def get_current_file_info():
    """获取当前文件信息"""
    software = get_current_software()
    
    try:
        if software == 'maya':
            import maya.cmds as cmds
            current_file = cmds.file(query=True, sceneName=True)
            modified = cmds.file(query=True, modified=True)
            return {
                'file': current_file,
                'modified': modified,
                'software': 'Maya'
            }
            
        elif software == 'blender':
            import bpy
            current_file = bpy.data.filepath
            modified = bpy.data.is_dirty
            return {
                'file': current_file,
                'modified': modified,
                'software': 'Blender'
            }
            
        elif software == 'max':
            import pymxs
            rt = pymxs.runtime
            current_file = rt.maxFilePath + rt.maxFileName
            modified = rt.getSaveRequired()
            return {
                'file': current_file,
                'modified': modified,
                'software': '3ds Max'
            }
            
        elif software == 'houdini':
            import hou
            current_file = hou.hipFile.path()
            modified = hou.hipFile.hasUnsavedChanges()
            return {
                'file': current_file,
                'modified': modified,
                'software': 'Houdini'
            }
            
    except Exception as e:
        print(f"获取文件信息失败: {e}")
    
    return {
        'file': None,
        'modified': False,
        'software': software
    }

def create_directory(path):
    """创建目录"""
    try:
        if not os.path.exists(path):
            os.makedirs(path)
            return True
        return True
    except Exception as e:
        print(f"创建目录失败: {e}")
        return False

def get_software_info():
    """获取软件详细信息"""
    software = get_current_software()
    info = {
        'name': software,
        'version': 'Unknown',
        'available': False
    }
    
    try:
        if software == 'maya':
            import maya.cmds as cmds
            info['version'] = cmds.about(version=True)
            info['available'] = True
            
        elif software == 'blender':
            import bpy
            info['version'] = bpy.app.version_string
            info['available'] = True
            
        elif software == 'max':
            import pymxs
            rt = pymxs.runtime
            info['version'] = str(rt.maxVersion()[0])
            info['available'] = True
            
        elif software == 'houdini':
            import hou
            info['version'] = hou.applicationVersionString()
            info['available'] = True
            
    except:
        pass
    
    return info
