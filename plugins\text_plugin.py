# -*- coding: utf-8 -*-
"""文本插件 | 作者: MMQ"""

import os
import subprocess
import platform
from plugins.base_plugin import PluginBase

class TextPlugin(PluginBase):
    """文本文件插件"""
    
    def __init__(self, file_extension=".txt"):
        self.file_extension = file_extension
        self.current_file = None
    
    def get_name(self):
        """返回插件名称"""
        return f"文本编辑器 ({self.file_extension})"
    
    def get_file_extension(self):
        """返回文件扩展名"""
        return self.file_extension
    
    def open_file(self, file_path):
        """打开文本文件"""
        try:
            system = platform.system()
            if system == "Windows":
                subprocess.Popen(['notepad', file_path])
            elif system == "Darwin":
                subprocess.Popen(['open', '-t', file_path])
            else:
                subprocess.Popen(['gedit', file_path])
            
            self.current_file = file_path
            return True
        except:
            return False
    
    def save_file(self, file_path):
        """保存文本文件"""
        try:
            if self.current_file and os.path.exists(self.current_file):
                import shutil
                shutil.copy2(self.current_file, file_path)
            else:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"# 新建文件: {os.path.basename(file_path)}\n")
                    f.write(f"# 创建时间: {os.path.basename(file_path)}\n\n")
            
            self.current_file = file_path
            return True
        except:
            return False
    
    def check_unsaved_changes(self):
        """检查是否有未保存的更改"""
        return False
    
    def get_current_file_info(self):
        """获取当前文件信息"""
        if self.current_file:
            return {
                'file': self.current_file,
                'modified': False,
                'exists': os.path.exists(self.current_file),
                'name': os.path.basename(self.current_file)
            }
        return {
            'file': None,
            'modified': False,
            'exists': False,
            'name': "untitled"
        }

class PythonPlugin(TextPlugin):
    """Python脚本插件"""
    
    def __init__(self):
        super().__init__(".py")
    
    def get_name(self):
        return "Python脚本编辑器"
    
    def save_file(self, file_path):
        """保存Python文件"""
        try:
            if self.current_file and os.path.exists(self.current_file):
                import shutil
                shutil.copy2(self.current_file, file_path)
            else:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("#!/usr/bin/env python\n")
                    f.write("# -*- coding: utf-8 -*-\n")
                    f.write(f'"""{os.path.basename(file_path)} - Python脚本"""\n\n')
                    f.write("def main():\n")
                    f.write('    print("Hello, World!")\n\n')
                    f.write('if __name__ == "__main__":\n')
                    f.write("    main()\n")
            
            self.current_file = file_path
            return True
        except:
            return False
