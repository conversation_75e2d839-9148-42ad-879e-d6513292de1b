# -*- coding: utf-8 -*-
"""
Maya简易文件管理器 - 立即运行版本
作者: MMQ
适用于: Maya 2022.5.1

直接复制粘贴到Maya脚本编辑器中运行，立即显示界面！
"""

print("🚀 正在启动Maya文件管理器...")

# 导入必要的模块
try:
    import maya.cmds as cmds
    import maya.mel as mel
    from maya.app.general.mayaMixin import MayaQWidgetDockableMixin
    print("✅ Maya模块导入成功")
except ImportError as e:
    print(f"❌ Maya模块导入失败: {e}")
    print("请确保在Maya环境中运行此脚本")
    raise

try:
    from PySide2.QtWidgets import (QWidget, QVBoxLayout, QPushButton, QLabel, 
                                   QFileDialog, QMessageBox, QTextEdit, QFrame)
    from PySide2.QtCore import Qt
    from PySide2.QtGui import QFont
    print("✅ PySide2模块导入成功")
except ImportError as e:
    print(f"❌ PySide2模块导入失败: {e}")
    print("请确保Maya版本支持PySide2")
    raise

import os
import datetime

class SimpleFileManager(MayaQWidgetDockableMixin, QWidget):
    """简易Maya文件管理器"""
    
    def __init__(self):
        super(SimpleFileManager, self).__init__()
        self.setWindowTitle("Maya文件管理器 - 简易版")
        self.setMinimumSize(400, 350)
        self.setObjectName("SimpleFileManager")
        
        # 创建界面
        self.setup_ui()
        
        # 记录启动信息
        self.log("文件管理器启动成功！")
        self.log(f"Maya版本: {cmds.about(version=True)}")
        
    def setup_ui(self):
        """创建用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title = QLabel("🗂️ Maya文件管理器")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title)
        
        # 打开文件按钮
        self.open_btn = QPushButton("📂 打开Maya文件")
        self.open_btn.setMinimumHeight(50)
        self.open_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        self.open_btn.clicked.connect(self.open_file)
        layout.addWidget(self.open_btn)
        
        # 保存文件按钮
        self.save_btn = QPushButton("💾 保存当前文件")
        self.save_btn.setMinimumHeight(50)
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        self.save_btn.clicked.connect(self.save_file)
        layout.addWidget(self.save_btn)
        
        # 另存为按钮
        self.save_as_btn = QPushButton("📁 另存为...")
        self.save_as_btn.setMinimumHeight(50)
        self.save_as_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
            QPushButton:pressed {
                background-color: #d35400;
            }
        """)
        self.save_as_btn.clicked.connect(self.save_as_file)
        layout.addWidget(self.save_as_btn)
        
        # 当前文件信息
        self.file_info = QLabel("当前文件: 新建场景")
        self.file_info.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 10px;
                font-size: 12px;
                color: #495057;
            }
        """)
        self.file_info.setWordWrap(True)
        layout.addWidget(self.file_info)
        
        # 日志区域
        log_label = QLabel("操作日志:")
        log_label.setStyleSheet("font-weight: bold; color: #495057;")
        layout.addWidget(log_label)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(120)
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 8px;
                font-family: monospace;
                font-size: 10px;
                color: #495057;
            }
        """)
        layout.addWidget(self.log_text)
        
        # 更新文件信息
        self.update_file_info()
        
    def log(self, message):
        """记录日志"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)
        print(f"[文件管理器] {message}")
        
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
        
    def update_file_info(self):
        """更新当前文件信息"""
        try:
            current_scene = cmds.file(query=True, sceneName=True)
            if current_scene:
                file_name = os.path.basename(current_scene)
                self.file_info.setText(f"当前文件: {file_name}\n路径: {current_scene}")
            else:
                self.file_info.setText("当前文件: 新建场景 (未保存)")
        except Exception as e:
            self.file_info.setText(f"获取文件信息失败: {str(e)}")
            
    def open_file(self):
        """打开文件"""
        try:
            self.log("正在打开文件选择对话框...")
            
            # 获取工作目录
            try:
                workspace = cmds.workspace(query=True, rootDirectory=True)
            except:
                workspace = os.path.expanduser("~")
                
            # 文件对话框
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择Maya文件",
                workspace,
                "Maya Files (*.ma *.mb);;Maya ASCII (*.ma);;Maya Binary (*.mb);;All Files (*.*)"
            )
            
            if file_path:
                # 检查未保存的更改
                if cmds.file(query=True, modified=True):
                    reply = QMessageBox.question(
                        self, "确认", 
                        "当前场景有未保存的更改。是否继续？",
                        QMessageBox.Yes | QMessageBox.No
                    )
                    if reply == QMessageBox.No:
                        self.log("用户取消了文件打开")
                        return
                
                # 打开文件
                cmds.file(file_path, open=True, force=True)
                self.log(f"成功打开文件: {os.path.basename(file_path)}")
                self.update_file_info()
                
                # 显示成功消息
                QMessageBox.information(self, "成功", f"文件已打开:\n{os.path.basename(file_path)}")
                
            else:
                self.log("用户取消了文件选择")
                
        except Exception as e:
            error_msg = f"打开文件时发生错误: {str(e)}"
            self.log(error_msg)
            QMessageBox.critical(self, "错误", error_msg)
            
    def save_file(self):
        """保存当前文件"""
        try:
            current_scene = cmds.file(query=True, sceneName=True)
            if current_scene:
                cmds.file(save=True)
                self.log(f"文件保存成功: {os.path.basename(current_scene)}")
                self.update_file_info()
                QMessageBox.information(self, "成功", "文件保存成功！")
            else:
                self.log("当前场景未命名，将打开另存为对话框")
                self.save_as_file()
        except Exception as e:
            error_msg = f"保存文件时发生错误: {str(e)}"
            self.log(error_msg)
            QMessageBox.critical(self, "错误", error_msg)
            
    def save_as_file(self):
        """另存为文件"""
        try:
            self.log("正在打开保存对话框...")

            # 指定默认保存目录
            default_save_dir = "X:/dev/Scgtest/0010/3d/mm/"

            # 检查目录是否存在，如果不存在则创建
            if not os.path.exists(default_save_dir):
                try:
                    os.makedirs(default_save_dir)
                    self.log(f"已创建目录: {default_save_dir}")
                except Exception as e:
                    self.log(f"无法创建目录 {default_save_dir}: {str(e)}")
                    # 如果无法创建指定目录，回退到Maya工作目录
                    try:
                        default_save_dir = cmds.workspace(query=True, rootDirectory=True)
                    except:
                        default_save_dir = os.path.expanduser("~")
                    self.log(f"使用备用目录: {default_save_dir}")
            else:
                self.log(f"使用指定保存目录: {default_save_dir}")

            # 默认文件名
            current_scene = cmds.file(query=True, sceneName=True)
            if current_scene:
                default_name = os.path.splitext(os.path.basename(current_scene))[0]
            else:
                default_name = "untitled"

            default_path = os.path.join(default_save_dir, default_name + ".ma")
            
            # 保存对话框
            file_path, selected_filter = QFileDialog.getSaveFileName(
                self,
                "保存Maya文件",
                default_path,
                "Maya ASCII (*.ma);;Maya Binary (*.mb)"
            )
            
            if file_path:
                # 确定文件类型
                if "ASCII" in selected_filter or file_path.endswith('.ma'):
                    file_type = "mayaAscii"
                    if not file_path.endswith('.ma'):
                        file_path += '.ma'
                else:
                    file_type = "mayaBinary"
                    if not file_path.endswith('.mb'):
                        file_path += '.mb'
                
                # 保存文件
                cmds.file(rename=file_path)
                cmds.file(save=True, type=file_type)
                
                self.log(f"文件另存为成功: {os.path.basename(file_path)}")
                self.update_file_info()
                QMessageBox.information(self, "成功", f"文件已保存:\n{os.path.basename(file_path)}")
                
            else:
                self.log("用户取消了文件保存")
                
        except Exception as e:
            error_msg = f"另存为文件时发生错误: {str(e)}"
            self.log(error_msg)
            QMessageBox.critical(self, "错误", error_msg)


# 全局变量
file_manager_window = None

def show_simple_file_manager():
    """显示简易文件管理器"""
    global file_manager_window
    
    try:
        # 如果窗口已存在，显示它
        if file_manager_window is not None:
            file_manager_window.show()
            file_manager_window.raise_()
            file_manager_window.activateWindow()
            print("✅ 文件管理器窗口已显示")
            return file_manager_window
            
        # 创建新窗口
        file_manager_window = SimpleFileManager()
        file_manager_window.show(dockable=True)
        
        print("🎉 Maya文件管理器界面已成功启动！")
        print("🎯 您现在可以使用图形界面来打开和保存Maya文件了！")
        return file_manager_window
        
    except Exception as e:
        print(f"❌ 启动文件管理器时发生错误: {str(e)}")
        return None

# 立即启动文件管理器
print("🔥 正在启动Maya文件管理器界面...")
window = show_simple_file_manager()

if window:
    print("✅ 启动成功！文件管理器界面已显示。")
    print("📝 如需重新打开，请运行: show_simple_file_manager()")
else:
    print("❌ 启动失败，请检查Maya环境。")
