# -*- coding: utf-8 -*-
"""菜单模块 | 作者: MMQ"""

from menus.base_menu import MenuBase

def create_menu(software):
    """菜单工厂函数"""
    try:
        if software == 'maya':
            from menus.maya_menu import MayaMenu
            return MayaMenu()
        elif software == 'blender':
            from menus.blender_menu import BlenderMenu
            return BlenderMenu()
        else:
            return None
    except ImportError:
        return None

def get_available_menus():
    """获取可用菜单列表"""
    menus = []
    
    # 检查Maya菜单
    try:
        from menus.maya_menu import MayaMenu
        menu = MayaMenu()
        if menu.is_available():
            menus.append(('maya', 'Maya菜单'))
    except:
        pass
    
    # 检查Blender菜单
    try:
        from menus.blender_menu import BlenderMenu
        menu = BlenderMenu()
        if menu.is_available():
            menus.append(('blender', 'Blender菜单'))
    except:
        pass
    
    return menus
