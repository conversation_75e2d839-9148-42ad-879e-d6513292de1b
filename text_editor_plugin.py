# -*- coding: utf-8 -*-
"""文本编辑器插件示例 | 作者: MMQ"""

import os
import subprocess
import platform
from universal_file_manager import FileManagerPlugin

class TextEditorPlugin(FileManagerPlugin):
    """文本编辑器插件"""
    
    def __init__(self, file_extension=".txt", editor_command=None):
        self.file_extension = file_extension
        self.editor_command = editor_command or self._get_default_editor()
        self.current_file = None
    
    def _get_default_editor(self):
        """获取默认编辑器命令"""
        system = platform.system()
        if system == "Windows":
            return "notepad"
        elif system == "Darwin":  # macOS
            return "open -t"
        else:  # Linux
            return "gedit"
    
    def get_name(self):
        """返回插件名称"""
        return f"文本编辑器 ({self.file_extension})"
    
    def get_file_extension(self):
        """返回文件扩展名"""
        return self.file_extension
    
    def open_file(self, file_path):
        """打开文本文件"""
        try:
            if platform.system() == "Windows":
                subprocess.Popen([self.editor_command, file_path])
            else:
                subprocess.Popen(self.editor_command.split() + [file_path])
            
            self.current_file = file_path
            print(f"✅ 成功打开文本文件: {os.path.basename(file_path)}")
            return True
        except Exception as e:
            print(f"❌ 打开文本文件失败: {e}")
            return False
    
    def save_file(self, file_path):
        """保存文本文件（创建空文件或复制当前文件）"""
        try:
            if self.current_file and os.path.exists(self.current_file):
                # 如果有当前文件，复制内容
                import shutil
                shutil.copy2(self.current_file, file_path)
            else:
                # 创建空文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"# 新建文件: {os.path.basename(file_path)}\n")
                    f.write(f"# 创建时间: {os.path.basename(file_path)}\n\n")
            
            self.current_file = file_path
            print(f"✅ 成功保存文本文件: {os.path.basename(file_path)}")
            return True
        except Exception as e:
            print(f"❌ 保存文本文件失败: {e}")
            return False
    
    def check_unsaved_changes(self):
        """检查是否有未保存的更改（文本编辑器无法直接检测）"""
        return False  # 文本编辑器无法直接检测未保存更改
    
    def get_current_file_info(self):
        """获取当前文件信息"""
        if self.current_file:
            return f"当前文件: {os.path.basename(self.current_file)}"
        else:
            return "当前文件: 无"

class PythonScriptPlugin(TextEditorPlugin):
    """Python脚本插件"""
    
    def __init__(self):
        super().__init__(".py", self._get_python_editor())
    
    def _get_python_editor(self):
        """获取Python编辑器"""
        system = platform.system()
        if system == "Windows":
            # 尝试找到常见的Python编辑器
            editors = ["code", "pycharm", "notepad++", "notepad"]
            for editor in editors:
                try:
                    subprocess.run([editor, "--version"], capture_output=True, timeout=2)
                    return editor
                except:
                    continue
            return "notepad"
        elif system == "Darwin":
            return "open -a TextEdit"
        else:
            return "gedit"
    
    def get_name(self):
        return "Python脚本编辑器"
    
    def save_file(self, file_path):
        """保存Python文件"""
        try:
            if self.current_file and os.path.exists(self.current_file):
                import shutil
                shutil.copy2(self.current_file, file_path)
            else:
                # 创建Python模板
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("#!/usr/bin/env python\n")
                    f.write("# -*- coding: utf-8 -*-\n")
                    f.write(f'"""{os.path.basename(file_path)} - Python脚本"""\n\n')
                    f.write("def main():\n")
                    f.write('    print("Hello, World!")\n\n')
                    f.write('if __name__ == "__main__":\n')
                    f.write("    main()\n")
            
            self.current_file = file_path
            print(f"✅ 成功保存Python文件: {os.path.basename(file_path)}")
            return True
        except Exception as e:
            print(f"❌ 保存Python文件失败: {e}")
            return False

def show_text_file_manager(file_extension=".txt", save_dir=None, base_name=None):
    """显示文本文件管理器"""
    try:
        from universal_file_manager import UniversalFileManager
        
        # 创建文本编辑器插件
        if file_extension == ".py":
            plugin = PythonScriptPlugin()
        else:
            plugin = TextEditorPlugin(file_extension)
        
        # 创建文件管理器
        manager = UniversalFileManager(
            plugin=plugin,
            save_dir=save_dir or os.path.expanduser("~/Documents/TextProjects"),
            base_name=base_name or "text_project"
        )
        
        manager.show()
        print(f"✅ {plugin.get_name()}文件管理器启动成功！")
        return manager
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return None

def show_python_file_manager(save_dir=None, base_name=None):
    """显示Python文件管理器"""
    return show_text_file_manager(".py", save_dir, base_name or "python_script")

# 中文函数别名
启动文本文件管理器 = lambda: show_text_file_manager()
启动Python文件管理器 = lambda: show_python_file_manager()

if __name__ == "__main__":
    # 独立运行时显示选择菜单
    try:
        from PySide2.QtWidgets import QApplication, QDialog, QVBoxLayout, QPushButton, QLabel
        import sys
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        class PluginSelector(QDialog):
            def __init__(self):
                super().__init__()
                self.setWindowTitle("选择文件类型")
                self.setFixedSize(300, 200)
                
                layout = QVBoxLayout(self)
                layout.addWidget(QLabel("请选择要管理的文件类型:"))
                
                txt_btn = QPushButton("文本文件 (.txt)")
                txt_btn.clicked.connect(lambda: self.start_manager(".txt"))
                layout.addWidget(txt_btn)
                
                py_btn = QPushButton("Python脚本 (.py)")
                py_btn.clicked.connect(lambda: self.start_manager(".py"))
                layout.addWidget(py_btn)
                
                self.manager = None
            
            def start_manager(self, ext):
                self.manager = show_text_file_manager(ext)
                self.accept()
        
        selector = PluginSelector()
        if selector.exec_() == QDialog.Accepted and selector.manager:
            sys.exit(app.exec_())
    
    except ImportError:
        print("GUI不可用，启动默认文本文件管理器")
        show_text_file_manager()
