# Maya文件管理器函数说明文档

## 📋 概述
Maya文件管理器是一个基于PySide2的GUI工具，用于管理Maya场景文件的版本控制。支持iPhone式版本选择、颜色区分、可调整界面等功能。

## 🏗️ 类结构

### `MayaFileManager(MayaQWidgetDockableMixin, QWidget)`
主要的文件管理器类，继承自Maya的可停靠窗口混合类和Qt的QWidget。

---

## 📚 函数方法说明（按代码顺序）

### 1. `__init__(self)` - 构造函数
**行号**: 15-26  
**功能**: 初始化文件管理器实例  
**参数**: 无  
**返回**: 无  
**说明**: 
- 设置窗口标题和尺寸（450x400 到 800x800）
- 配置保存目录和文件基础名称
- 初始化日志折叠状态
- 调用界面创建和文件列表更新

### 2. `create_ui(self)` - 创建用户界面
**行号**: 28-171  
**功能**: 构建完整的用户界面  
**参数**: 无  
**返回**: 无  
**说明**: 
- 创建垂直布局管理器
- 设置Maya风格的样式表
- 创建标题、版本选择器、信息显示区、按钮、日志区域
- 配置各组件的尺寸和样式

### 3. `toggle_log(self)` - 切换日志显示
**行号**: 173-181  
**功能**: 展开或折叠操作日志区域  
**参数**: 无  
**返回**: 无  
**说明**: 
- 切换日志展开状态
- 更新按钮文字（▶/▼）
- 显示或隐藏日志文本区域

### 4. `log(self, msg)` - 记录日志
**行号**: 183-187  
**功能**: 添加带时间戳的日志信息  
**参数**: 
- `msg` (str): 要记录的消息
**返回**: 无  
**说明**: 
- 添加当前时间戳
- 同时输出到界面和控制台
- 自动滚动到最新日志

### 5. `get_existing_versions(self)` - 获取已存在版本
**行号**: 189-200  
**功能**: 扫描目录获取现有文件版本号  
**参数**: 无  
**返回**: `list` - 版本号列表（降序）  
**说明**: 
- 扫描指定目录下的.ma/.mb文件
- 解析文件名中的版本号（格式：v001, v002等）
- 返回去重后的降序版本列表

### 6. `generate_version_list(self)` - 生成版本列表
**行号**: 202-234  
**功能**: 生成包含现有和预置版本的完整列表  
**参数**: 无  
**返回**: `list` - 完整版本列表  
**说明**: 
- 获取现有版本的最大版本号
- 在最大版本基础上预置5个未来版本
- 确保至少包含v001-v005
- 按版本号降序排列

### 7. `update_file_list(self)` - 更新版本选择器
**行号**: 236-299  
**功能**: 刷新版本选择器的内容和颜色  
**参数**: 无  
**返回**: 无  
**说明**: 
- 清空并重新填充版本选择器
- 设置版本颜色：绿色（最新）、红色（历史）、白色（新版本）
- 智能选择默认版本：有文件选最新，无文件选v001
- 记录操作日志

### 8. `on_version_selected(self, display_text)` - 版本选择回调
**行号**: 301-370  
**功能**: 处理版本选择变化事件  
**参数**: 
- `display_text` (str): 显示的文本内容
**返回**: 无  
**说明**: 
- 获取选中版本的详细信息
- 更新选择器颜色和状态图标
- 显示文件大小、修改时间、路径等信息
- 存储当前文件路径用于后续操作

### 9. `open_file_location(self, event)` - 打开文件位置
**行号**: 372-426  
**功能**: 在系统文件管理器中打开文件位置  
**参数**: 
- `event`: 鼠标点击事件
**返回**: 无  
**说明**: 
- 跨平台支持（Windows/macOS/Linux）
- 文件存在时选中文件，不存在时打开目录
- 自动创建不存在的目录
- 备用方案：打开默认保存目录

### 10. `filename(self, ver)` - 生成文件名
**行号**: 428-429  
**功能**: 根据版本号生成标准文件名  
**参数**: 
- `ver` (str): 版本号（如v001）
**返回**: `str` - 完整文件名  
**说明**: 
- 使用固定格式：基础名_版本号_mmq.ma
- 确保文件名的一致性

### 11. `confirm_overwrite(self, path, ver)` - 确认覆盖
**行号**: 431-440  
**功能**: 询问用户是否覆盖已存在的文件  
**参数**: 
- `path` (str): 文件完整路径
- `ver` (str): 版本号
**返回**: `bool` - 用户确认结果  
**说明**: 
- 检查文件是否存在
- 显示确认对话框
- 记录用户选择

### 12. `open_selected_file(self)` - 打开选中文件
**行号**: 442-475  
**功能**: 打开当前选择的Maya文件  
**参数**: 无  
**返回**: 无  
**说明**: 
- 验证文件选择和存在性
- 检查当前场景的未保存更改
- 使用Maya命令打开文件
- 显示操作结果

### 13. `save_to_selected(self)` - 保存到选中版本
**行号**: 477-515  
**功能**: 将当前场景保存为选中版本  
**参数**: 无  
**返回**: 无  
**说明**: 
- 验证版本选择
- 创建必要的目录
- 确认覆盖已存在文件
- 执行Maya保存命令
- 刷新版本列表

---

## 🔧 全局函数

### 14. `close_existing()` - 关闭现有窗口
**行号**: 520-527  
**功能**: 清理已存在的文件管理器窗口  
**参数**: 无  
**返回**: 无  
**说明**: 
- 检查并删除现有的工作区控件和窗口
- 防止重复创建窗口

### 15. `show_manager()` - 显示文件管理器
**行号**: 529-547  
**功能**: 创建并显示文件管理器实例  
**参数**: 无  
**返回**: `MayaFileManager` 或 `None`  
**说明**: 
- 清理现有窗口
- 创建新的文件管理器实例
- 以可停靠模式显示
- 返回创建的实例

---

## 🎨 颜色系统
- **🟢 绿色**: 最新已存在版本
- **🔴 红色**: 历史版本
- **⚪ 白色**: 未创建版本

## 📁 文件路径格式
```
D:/dev/Scgtest/0010/3d/mm/dev_cgtest_0010_mm_v001_mmq.ma
```

## 🚀 启动方式
```python
# 直接运行脚本或调用
show_manager()  # 或
重启文件管理器()
```
