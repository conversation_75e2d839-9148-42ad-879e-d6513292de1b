# -*- coding: utf-8 -*-
"""
Maya文件管理器插件测试脚本
作者: MMQ

此脚本用于测试插件的各项功能
在Maya脚本编辑器中运行此脚本来验证插件是否正常工作
"""

import sys
import os

def test_plugin_import():
    """测试插件导入"""
    print("=" * 50)
    print("测试1: 插件导入")
    print("=" * 50)
    
    try:
        import maya_file_manager
        print("✓ 插件导入成功")
        return True
    except ImportError as e:
        print(f"✗ 插件导入失败: {e}")
        return False

def test_maya_dependencies():
    """测试Maya依赖"""
    print("\n" + "=" * 50)
    print("测试2: Maya依赖检查")
    print("=" * 50)
    
    try:
        import maya.cmds as cmds
        print("✓ maya.cmds 导入成功")
        
        import maya.mel as mel
        print("✓ maya.mel 导入成功")
        
        from maya.app.general.mayaMixin import MayaQWidgetDockableMixin
        print("✓ MayaQWidgetDockableMixin 导入成功")
        
        return True
    except ImportError as e:
        print(f"✗ Maya依赖导入失败: {e}")
        return False

def test_pyside2_dependencies():
    """测试PySide2依赖"""
    print("\n" + "=" * 50)
    print("测试3: PySide2依赖检查")
    print("=" * 50)
    
    try:
        from PySide2.QtWidgets import QWidget, QVBoxLayout, QPushButton
        print("✓ PySide2.QtWidgets 导入成功")
        
        from PySide2.QtCore import Qt, Signal
        print("✓ PySide2.QtCore 导入成功")
        
        from PySide2.QtGui import QFont
        print("✓ PySide2.QtGui 导入成功")
        
        return True
    except ImportError as e:
        print(f"✗ PySide2依赖导入失败: {e}")
        return False

def test_plugin_functions():
    """测试插件功能"""
    print("\n" + "=" * 50)
    print("测试4: 插件功能测试")
    print("=" * 50)
    
    try:
        import maya_file_manager
        
        # 测试显示文件管理器
        print("正在测试显示文件管理器...")
        window = maya_file_manager.show_file_manager()
        if window:
            print("✓ 文件管理器窗口创建成功")
            
            # 测试窗口属性
            if hasattr(window, 'open_button') and hasattr(window, 'save_button'):
                print("✓ 界面元素创建成功")
            else:
                print("✗ 界面元素缺失")
                return False
            
            # 测试关闭功能
            maya_file_manager.close_file_manager()
            print("✓ 文件管理器关闭成功")
            
        else:
            print("✗ 文件管理器窗口创建失败")
            return False
        
        return True
    except Exception as e:
        print(f"✗ 插件功能测试失败: {e}")
        return False

def test_menu_installation():
    """测试菜单安装"""
    print("\n" + "=" * 50)
    print("测试5: 菜单安装测试")
    print("=" * 50)
    
    try:
        import maya_file_manager
        import maya.cmds as cmds
        
        # 安装菜单
        maya_file_manager.install_menu()
        
        # 检查菜单是否存在
        if cmds.menu("FileManagerMenu", exists=True):
            print("✓ 文件管理器菜单安装成功")
            
            # 卸载菜单
            maya_file_manager.uninstall_menu()
            print("✓ 文件管理器菜单卸载成功")
            
            return True
        else:
            print("✗ 文件管理器菜单安装失败")
            return False
            
    except Exception as e:
        print(f"✗ 菜单安装测试失败: {e}")
        return False

def test_workspace_detection():
    """测试工作空间检测"""
    print("\n" + "=" * 50)
    print("测试6: 工作空间检测")
    print("=" * 50)
    
    try:
        import maya.cmds as cmds
        
        workspace = cmds.workspace(query=True, rootDirectory=True)
        print(f"✓ 当前工作空间: {workspace}")
        
        if os.path.exists(workspace):
            print("✓ 工作空间目录存在")
            return True
        else:
            print("✗ 工作空间目录不存在")
            return False
            
    except Exception as e:
        print(f"✗ 工作空间检测失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("Maya文件管理器插件测试开始")
    print("=" * 60)
    
    tests = [
        test_maya_dependencies,
        test_pyside2_dependencies,
        test_plugin_import,
        test_workspace_detection,
        test_menu_installation,
        test_plugin_functions
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试执行异常: {e}")
    
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    print(f"总测试数: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    print(f"成功率: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！插件可以正常使用。")
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查插件安装。")
    
    print("=" * 60)

def quick_test():
    """快速测试 - 仅测试基本功能"""
    print("快速测试模式")
    print("=" * 30)
    
    try:
        # 测试导入
        import maya_file_manager
        print("✓ 插件导入成功")
        
        # 测试显示
        window = maya_file_manager.show_file_manager()
        if window:
            print("✓ 界面显示成功")
            maya_file_manager.close_file_manager()
            print("✓ 界面关闭成功")
            print("\n🎉 快速测试通过！")
        else:
            print("✗ 界面显示失败")
            
    except Exception as e:
        print(f"✗ 快速测试失败: {e}")

if __name__ == "__main__":
    # 如果作为脚本运行，执行快速测试
    quick_test()

# 在Maya脚本编辑器中运行完整测试的命令：
# exec(open(r"path/to/test_plugin.py").read())
# run_all_tests()

# 在Maya脚本编辑器中运行快速测试的命令：
# exec(open(r"path/to/test_plugin.py").read())
# quick_test()
