# -*- coding: utf-8 -*-
"""控制器 | 作者: MMQ"""

import os
from mvc.model import FileManagerModel
from mvc.view import FileManagerView
from utils import open_file_location, ensure_directory

class FileManagerController:
    """文件管理器控制器"""
    
    def __init__(self, plugin, save_dir: str, base_name: str, parent=None):
        # 创建模型和视图
        self.model = FileManagerModel(plugin, save_dir, base_name)
        self.view = FileManagerView(parent)
        
        # 连接信号
        self.connect_signals()
        
        # 初始化界面
        self.initialize_view()
        
        # 刷新数据
        self.refresh_data()
    
    def connect_signals(self):
        """连接视图信号到控制器方法"""
        self.view.version_selected.connect(self.on_version_selected)
        self.view.open_requested.connect(self.open_selected_file)
        self.view.save_requested.connect(self.save_to_selected)
        self.view.folder_open_requested.connect(self.open_project_folder)
    
    def initialize_view(self):
        """初始化视图"""
        plugin_name = self.model.plugin.get_name() if self.model.plugin else "通用"
        title = f"文件管理器 - {plugin_name}"
        self.view.update_title(title)
        
        self.log("文件管理器启动成功")
    
    def refresh_data(self):
        """刷新数据和界面"""
        try:
            # 刷新模型数据
            self.model.refresh()
            
            # 生成版本列表
            versions = self.model.generate_version_list()
            
            # 获取版本数据
            version_data = {}
            for version in versions:
                info = self.model.get_file_info(version)
                version_data[version] = info
            
            # 更新视图
            self.view.update_versions(versions, version_data)
            
            # 设置默认版本
            default_version = self.model.get_default_version()
            self.view.set_current_version(default_version)
            self.on_version_selected(default_version)
            
            self.log(f"版本列表已更新，共 {len(versions)} 个版本")
            
        except Exception as e:
            self.log(f"刷新数据失败: {e}")
    
    def on_version_selected(self, version: str):
        """版本选择改变处理"""
        try:
            self.model.set_current_version(version)
            info = self.model.get_file_info(version)
            
            # 构建信息文本
            info_text = self.build_info_text(info)
            self.view.update_info(info_text)
            
            # 更新按钮状态
            open_enabled = info['exists']
            self.view.set_buttons_enabled(open_enabled, True)
            
            self.log(f"选择版本: {version}")
            
        except Exception as e:
            self.log(f"版本选择处理失败: {e}")
    
    def build_info_text(self, info: dict) -> str:
        """构建信息显示文本"""
        version = info['version']
        filename = info['name']
        icon = info['icon']
        status_text = info['status_text']
        
        text = f"当前选择: {icon} {version}\n"
        text += f"文件名: {filename}\n"
        text += f"状态: {status_text}\n"
        
        if info['exists']:
            text += f"大小: {info['size']:.2f} MB\n"
            text += f"修改时间: {info['modified']}\n"
        
        text += f"\n📁 文件路径:\n{info['path']}\n"
        text += "(点击此区域打开文件夹)"
        
        return text
    
    def open_selected_file(self):
        """打开选中的文件"""
        if not self.model.plugin:
            self.view.show_message("错误", "没有可用的插件", "error")
            return
        
        try:
            current_version = self.model.get_current_version()
            if not current_version:
                self.view.show_message("错误", "请先选择要打开的文件版本", "warning")
                return
            
            file_path = self.model.get_file_path(current_version)
            
            if not os.path.exists(file_path):
                self.view.show_message("错误", 
                    f"版本 {current_version} 的文件不存在:\n{os.path.basename(file_path)}", 
                    "warning")
                return
            
            # 检查未保存更改
            if self.model.plugin.check_unsaved_changes():
                if not self.view.show_confirmation("确认", "当前有未保存的更改，是否继续打开？"):
                    self.log("用户取消打开操作")
                    return
            
            # 打开文件
            success = self.model.plugin.open_file(file_path)
            if success:
                self.log(f"成功打开: {os.path.basename(file_path)}")
                self.view.show_message("成功", 
                    f"已打开版本 {current_version}:\n{os.path.basename(file_path)}")
            else:
                self.log(f"打开失败: {os.path.basename(file_path)}")
                self.view.show_message("错误", f"无法打开文件: {os.path.basename(file_path)}", "error")
                
        except Exception as e:
            error_msg = f"打开文件失败: {str(e)}"
            self.log(error_msg)
            self.view.show_message("错误", error_msg, "error")
    
    def save_to_selected(self):
        """保存到选中版本"""
        if not self.model.plugin:
            self.view.show_message("错误", "没有可用的插件", "error")
            return
        
        try:
            current_version = self.model.get_current_version()
            if not current_version:
                self.view.show_message("错误", "请先选择要保存的版本", "warning")
                return
            
            file_path = self.model.get_file_path(current_version)
            
            # 确保目录存在
            if not ensure_directory(self.model.save_dir):
                self.view.show_message("错误", f"无法创建保存目录: {self.model.save_dir}", "error")
                return
            
            # 检查覆盖
            if os.path.exists(file_path):
                if not self.view.show_confirmation("文件已存在", 
                    f"版本 {current_version} 已存在，是否覆盖？\n\n{os.path.basename(file_path)}"):
                    self.log("用户取消覆盖")
                    return
            
            # 保存文件
            self.log(f"正在保存到版本 {current_version}: {os.path.basename(file_path)}")
            success = self.model.plugin.save_file(file_path)
            
            if success:
                self.log(f"保存成功: {os.path.basename(file_path)}")
                self.refresh_data()  # 刷新界面
                self.view.show_message("成功", 
                    f"文件已保存为版本 {current_version}:\n{os.path.basename(file_path)}")
            else:
                self.log(f"保存失败: {os.path.basename(file_path)}")
                self.view.show_message("错误", f"无法保存文件: {os.path.basename(file_path)}", "error")
                
        except Exception as e:
            error_msg = f"保存文件失败: {str(e)}"
            self.log(error_msg)
            self.view.show_message("错误", error_msg, "error")
    
    def open_project_folder(self):
        """打开项目文件夹"""
        try:
            current_version = self.model.get_current_version()
            if current_version:
                file_path = self.model.get_file_path(current_version)
            else:
                file_path = self.model.save_dir
            
            success = open_file_location(file_path)
            if success:
                self.log(f"已打开文件位置: {os.path.dirname(file_path)}")
            else:
                self.log("打开文件位置失败")
                
        except Exception as e:
            self.log(f"打开文件位置失败: {e}")
    
    def create_new_version(self):
        """创建新版本"""
        try:
            next_version = self.model.get_next_version()
            self.view.set_current_version(next_version)
            self.log(f"创建新版本: {next_version}")
        except Exception as e:
            self.log(f"创建新版本失败: {e}")
    
    def open_latest_version(self):
        """打开最新版本"""
        try:
            latest_version = self.model.get_latest_version()
            if latest_version:
                self.view.set_current_version(latest_version)
                self.open_selected_file()
            else:
                self.view.show_message("提示", "没有找到已存在的版本", "info")
        except Exception as e:
            self.log(f"打开最新版本失败: {e}")
    
    def save_current_version(self):
        """保存当前版本"""
        self.save_to_selected()
    
    def show_settings(self):
        """显示设置"""
        # 这里可以实现设置对话框
        project_info = self.model.get_project_info()
        info_text = f"项目信息:\n"
        info_text += f"保存目录: {project_info['save_dir']}\n"
        info_text += f"项目名称: {project_info['base_name']}\n"
        info_text += f"文件扩展名: {project_info['extension']}\n"
        info_text += f"总版本数: {project_info['total_versions']}\n"
        info_text += f"最新版本: {project_info['latest_version']}\n"
        info_text += f"总文件大小: {project_info['total_size_mb']:.2f} MB\n"
        info_text += f"插件: {project_info['plugin_name']}"
        
        self.view.show_message("项目设置", info_text, "info")
    
    def log(self, message: str):
        """记录日志"""
        self.view.add_log(message)
    
    def show(self):
        """显示界面"""
        self.view.show()
    
    def close(self):
        """关闭界面"""
        self.view.close()
    
    def get_view(self):
        """获取视图对象"""
        return self.view
    
    def get_model(self):
        """获取模型对象"""
        return self.model
