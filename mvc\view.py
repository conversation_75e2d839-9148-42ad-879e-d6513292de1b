# -*- coding: utf-8 -*-
"""视图层 - MVC架构的View层 | 作者: MMQ"""

import os
from typing import Callable, Optional, Dict, List

try:
    from PySide2.QtWidgets import *
    from PySide2.QtCore import *
    from PySide2.QtGui import *
except ImportError:
    try:
        from PyQt5.QtWidgets import *
        from PyQt5.QtCore import *
        from PyQt5.QtGui import *
    except ImportError:
        raise ImportError("需要安装PySide2或PyQt5")

from utils import TimeUtils

class FileManagerView(QWidget):
    """文件管理器视图"""
    
    # 信号定义
    version_selected = Signal(str)
    open_requested = Signal()
    save_requested = Signal()
    folder_open_requested = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.log_expanded = False
        self.setup_ui()
        self.setup_styles()
        self.connect_signals()
    
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("文件管理器")
        self.setMinimumSize(450, 400)
        self.setMaximumSize(800, 800)
        self.resize(500, 500)
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setSpacing(6)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 标题
        self.title_label = QLabel("文件管理器")
        self.title_label.setStyleSheet(
            "font-size:14px;font-weight:bold;color:#FFFFFF;"
            "padding:8px 0;border-bottom:1px solid #5A5A5A;margin-bottom:8px;"
        )
        layout.addWidget(self.title_label)
        
        # 版本选择器
        self.create_version_selector(layout)
        
        # 信息显示区
        self.create_info_area(layout)
        
        # 按钮区域
        self.create_buttons(layout)
        
        # 日志区域
        self.create_log_area(layout)
        
        layout.addStretch()
    
    def create_version_selector(self, layout):
        """创建版本选择器"""
        # 标签
        label = QLabel("文件版本选择器:")
        label.setStyleSheet("font-weight:bold;margin-bottom:4px;margin-top:8px;")
        layout.addWidget(label)
        
        # 版本选择器
        self.version_selector = QComboBox()
        self.version_selector.setFixedHeight(40)
        self.version_selector.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        layout.addWidget(self.version_selector)
    
    def create_info_area(self, layout):
        """创建信息显示区域"""
        self.info_area = QTextEdit()
        self.info_area.setMinimumHeight(80)
        self.info_area.setMaximumHeight(200)
        self.info_area.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.info_area.setReadOnly(True)
        self.info_area.setCursor(Qt.PointingHandCursor)
        layout.addWidget(self.info_area)
    
    def create_buttons(self, layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        self.open_btn = QPushButton("打开文件")
        self.open_btn.setFixedHeight(40)
        self.open_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        button_layout.addWidget(self.open_btn)
        
        self.save_btn = QPushButton("保存文件")
        self.save_btn.setFixedHeight(40)
        self.save_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        button_layout.addWidget(self.save_btn)
        
        layout.addLayout(button_layout)
    
    def create_log_area(self, layout):
        """创建日志区域"""
        layout.addSpacing(10)
        
        # 日志切换按钮
        log_header = QHBoxLayout()
        self.log_toggle_btn = QPushButton("▶ 操作日志")
        self.log_toggle_btn.setFixedHeight(25)
        self.log_toggle_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.log_toggle_btn.setStyleSheet(
            "text-align:left;padding:4px;font-weight:bold;"
            "border:none;background-color:transparent;"
        )
        log_header.addWidget(self.log_toggle_btn)
        log_header.addStretch()
        layout.addLayout(log_header)
        
        # 日志文本区域
        self.log_text = QTextEdit()
        self.log_text.setMinimumHeight(60)
        self.log_text.setMaximumHeight(150)
        self.log_text.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.log_text.setReadOnly(True)
        self.log_text.hide()  # 默认隐藏
        layout.addWidget(self.log_text)
    
    def setup_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QWidget {
                background-color: #393939;
                color: #CCCCCC;
                font-family: "Segoe UI";
                font-size: 11px;
            }
            QPushButton {
                background-color: #4A4A4A;
                border: 1px solid #5A5A5A;
                color: #CCCCCC;
                padding: 8px 16px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5A5A5A;
            }
            QPushButton:pressed {
                background-color: #3A3A3A;
            }
            QComboBox {
                background-color: #4A4A4A;
                border: 2px solid #5A5A5A;
                color: #FFFFFF;
                padding: 8px 12px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
            }
            QComboBox:hover {
                border-color: #6A6A6A;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                width: 0;
                height: 0;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-top: 6px solid #FFFFFF;
                margin-right: 6px;
            }
            QComboBox QAbstractItemView {
                background-color: #4A4A4A;
                border: 2px solid #5A5A5A;
                selection-background-color: #6A6A6A;
                outline: none;
                font-size: 13px;
                padding: 4px;
            }
            QComboBox QAbstractItemView::item {
                padding: 6px 8px;
                border-bottom: 1px solid #5A5A5A;
            }
            QTextEdit {
                background-color: #4A4A4A;
                border: 1px solid #5A5A5A;
                color: #CCCCCC;
                padding: 8px;
                border-radius: 3px;
            }
            QTextEdit:hover {
                border-color: #6A6A6A;
            }
            QLabel {
                color: #CCCCCC;
                background-color: transparent;
            }
        """)
        
        # 日志区域特殊样式
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #2A2A2A;
                border: 1px solid #5A5A5A;
                color: #CCCCCC;
                padding: 4px;
                border-radius: 3px;
                font-family: monospace;
                font-size: 10px;
            }
        """)
    
    def connect_signals(self):
        """连接信号"""
        self.version_selector.currentTextChanged.connect(self.on_version_changed)
        self.open_btn.clicked.connect(self.open_requested.emit)
        self.save_btn.clicked.connect(self.save_requested.emit)
        self.info_area.mousePressEvent = self.on_info_area_clicked
        self.log_toggle_btn.clicked.connect(self.toggle_log)
    
    def on_version_changed(self, text):
        """版本选择改变"""
        if text:
            current_index = self.version_selector.currentIndex()
            version = self.version_selector.itemData(current_index)
            if version:
                self.version_selected.emit(version)
    
    def on_info_area_clicked(self, event):
        """信息区域点击"""
        self.folder_open_requested.emit()
    
    def toggle_log(self):
        """切换日志显示"""
        self.log_expanded = not self.log_expanded
        if self.log_expanded:
            self.log_text.show()
            self.log_toggle_btn.setText("▼ 操作日志")
        else:
            self.log_text.hide()
            self.log_toggle_btn.setText("▶ 操作日志")
    
    def update_title(self, title: str):
        """更新标题"""
        self.title_label.setText(title)
        self.setWindowTitle(title)
    
    def update_versions(self, versions: List[str], version_data: Dict[str, Dict]):
        """更新版本列表"""
        self.version_selector.clear()
        
        for i, version in enumerate(versions):
            data = version_data.get(version, {})
            filename = data.get('name', f"file_{version}")
            display_text = f"{version} - {filename}"
            
            self.version_selector.addItem(display_text, version)
            
            # 设置颜色
            try:
                item = self.version_selector.model().item(i)
                color = data.get('color', (255, 255, 255))
                item.setForeground(QColor(*color))
            except:
                pass
    
    def set_current_version(self, version: str):
        """设置当前选择的版本"""
        for i in range(self.version_selector.count()):
            if self.version_selector.itemData(i) == version:
                self.version_selector.setCurrentIndex(i)
                break
    
    def update_info(self, info_text: str):
        """更新信息显示"""
        self.info_area.setPlainText(info_text)
    
    def add_log(self, message: str):
        """添加日志"""
        timestamp = TimeUtils.get_timestamp()
        self.log_text.append(f"[{timestamp}] {message}")
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum()
        )
    
    def clear_log(self):
        """清除日志"""
        self.log_text.clear()
    
    def show_message(self, title: str, message: str, msg_type: str = "info"):
        """显示消息对话框"""
        if msg_type == "warning":
            QMessageBox.warning(self, title, message)
        elif msg_type == "error":
            QMessageBox.critical(self, title, message)
        elif msg_type == "question":
            return QMessageBox.question(self, title, message, 
                                      QMessageBox.Yes | QMessageBox.No)
        else:
            QMessageBox.information(self, title, message)
    
    def show_confirmation(self, title: str, message: str) -> bool:
        """显示确认对话框"""
        reply = QMessageBox.question(self, title, message,
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)
        return reply == QMessageBox.Yes
    
    def get_current_version(self) -> Optional[str]:
        """获取当前选择的版本"""
        current_index = self.version_selector.currentIndex()
        if current_index >= 0:
            return self.version_selector.itemData(current_index)
        return None
    
    def set_buttons_enabled(self, open_enabled: bool = True, save_enabled: bool = True):
        """设置按钮启用状态"""
        self.open_btn.setEnabled(open_enabled)
        self.save_btn.setEnabled(save_enabled)
