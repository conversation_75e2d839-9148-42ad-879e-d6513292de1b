#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""通用文件管理器启动器 | 作者: MMQ"""

def launch_maya():
    """启动Maya文件管理器"""
    try:
        exec(open('maya_plugin.py').read())
    except Exception as e:
        print(f"Maya启动失败: {e}")

def launch_blender():
    """启动Blender文件管理器"""
    try:
        exec(open('blender_plugin.py').read())
    except Exception as e:
        print(f"Blender启动失败: {e}")

def launch_text():
    """启动文本文件管理器"""
    try:
        exec(open('text_editor_plugin.py').read())
    except Exception as e:
        print(f"文本编辑器启动失败: {e}")

def create_menu():
    """创建DCC菜单"""
    try:
        exec(open('dcc_menu_manager.py').read())
    except Exception as e:
        print(f"菜单创建失败: {e}")

# 快捷启动函数
maya = launch_maya
blender = launch_blender
text = launch_text
menu = create_menu

if __name__ == "__main__":
    print("通用文件管理器启动器")
    print("maya() - 启动Maya版本")
    print("blender() - 启动Blender版本") 
    print("text() - 启动文本编辑器版本")
    print("menu() - 创建DCC菜单")
    
    # 默认启动文本版本
    launch_text()
